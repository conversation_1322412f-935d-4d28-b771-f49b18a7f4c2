<?php

namespace App\Console\Commands\Tests;

use App\Models\Loan;
use App\Services\IdentityValidationService;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class ResetWrittenOffEntries extends Command
{
  protected $signature = 'lms:reset-write-off {loanId}';
  protected $description = 'Get access token for nin verification';

  public function handle(): int
  {
    $loan = Loan::query()->find($this->argument('loanId'));

    if (!$loan) {
        $this->error('Loan not found');

        return 1;
    }

    $this->info('Resetting...');

    return 0;
  }
}
