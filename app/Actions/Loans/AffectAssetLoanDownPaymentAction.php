<?php

namespace App\Actions\Loans;

use App\Helpers\AssetLoans;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\LoanProductTerm;
use App\Models\Transaction;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AffectAssetLoanDownPaymentAction
{
    public function execute(Transaction $transaction): bool
    {
        DB::beginTransaction();
        try {
            $customer = $transaction->customer;
            if (!$customer) {
                throw new Exception('Customer that performed the transaction was not found. Of they changed their phone number.');
            }

            $partner = $transaction->partner;
            if (!$partner) {
                throw new Exception('Partner that initiated the transaction was not found. Of they changed their phone number.');
            }


            $loanApplication = LoanApplication::find($transaction->Loan_Application_ID);
            if (!$loanApplication) {
                throw new Exception('Loan Application that initiated the transaction was not found. Of they changed their phone number.');
            }

            $loan = Loan::where('Loan_Application_ID', $transaction->Loan_Application_ID)->latest()->first();
            if ($loan) {
                throw new Exception('This application already has a loan.', 400);
            }

            $loanApplicationSession = $loanApplication->loan_session;

            $loanProduct = $loanApplication->loan_product;
            if (!$loanProduct) {
                throw new Exception('No Loan Product was found for this loan application.');
            }

            $loanProductTerm = LoanProductTerm::where('Code', $loanApplicationSession->Loan_Producd_Term_Code)->first();
            if (!$loanProductTerm) {
                throw new Exception('No Loan Product Term was found for this loan application.');
            }

            $loanApplicationSession->loanStatus = 'Approved';
            $loanApplicationSession->Pay_Off = $transaction->Amount;
            $transaction->Asset_Disbursement_Status = 'Pending';

            AssetLoans::affectDownpaymentAccounts($loanApplication, $loanApplicationSession->Down_Payment);

            DB::commit();
            return true;
        } catch (\Throwable $throwable) {
            DB::rollBack();
            Log::error($throwable->getMessage(), [
                "line" => $throwable->getLine(),
                "trace" => $throwable->getTrace()
            ]);
            return false;
        }
    }
}
