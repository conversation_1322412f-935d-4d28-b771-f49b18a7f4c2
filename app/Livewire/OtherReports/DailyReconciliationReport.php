<?php

namespace App\Livewire\OtherReports;

use App\Actions\OtherReports\GetDailyReconciliationReportDetailsAction;
use App\Services\Account\AccountSeederService;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\DailyReconciliationExport;

class DailyReconciliationReport extends Component
{
    use WithPagination, ExportsData;

    public string $accountType = AccountSeederService::COLLECTION_OVA_SLUG;

    public function mount()
    {
        $this->startDate = now()->toDateString();
        $this->endDate = now()->toDateString();
    }

    public function render()
    {
        return view('livewire.reports.daily-reconciliation-report', $this->getViewData());
    }

    public function printReport()
    {
        // Get data to pass to the report
        $viewData = $this->getViewData();
        $viewData['filters'] = $this->getFormattedDateFilters();
        $viewData['partnerName'] = auth()->user()?->partner->Institution_Name;

        return app(PdfGeneratorService::class)
            ->view('pdf.daily-reconciliation', $viewData)
            ->streamFromLivewire();
    }

    public function excelExport()
    {
        return Excel::download(new DailyReconciliationExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetDailyReconciliationReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }

    protected function getFilters(): array
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'accountType' => $this->accountType
        ];
    }

    protected function getViewData(): array
    {
        $records = $this->getReportData();

        return [
            'records' => $records,
            'openingRecord' => $records->first(),
            'closingRecord' => $records->last(),
        ];
    }
}
