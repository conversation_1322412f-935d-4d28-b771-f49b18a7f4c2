<?php

namespace App\Jobs;

use App\Exceptions\MtnApiException;
use App\Models\Customer;
use App\Models\Partner;
use App\Notifications\SmsNotification;
use App\Services\MtnApiService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class CompleteCustomerRegistration implements ShouldQueue
{
    use Queueable;

    protected MtnApiService $mtnApiService;

    public function __construct(public Customer $customer)
    {
        $this->mtnApiService = new MtnApiService();
    }

    /**
     * @throws GuzzleException
     */
    public function handle(): void
    {
        // todo: Determine how we can specify the partner to be used.
        $partner = Partner::query()->firstWhere('Identification_Code', 'CB009');

        if (empty($partner)) {
            return;
        }

        $registrationCompleted = $this->mtnApiService->customerRegistrationCompleted($this->customer);
        $message = 'Hello ' . $this->customer->name . ', you have successfully registered for the Weekend Agent Loan service.';

        if (! $registrationCompleted) {
            $message = 'Hello ' . $this->customer->name . ', registration for the Weekend Agent Loan service has failed.';
        }

        $this->customer->notify(new SmsNotification($message, $this->customer->Telephone_Number, $this->customer->id, $partner->id));
    }
}
