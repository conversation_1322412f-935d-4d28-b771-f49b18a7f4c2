<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\Customer;
use Illuminate\Http\Response;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class CustomerApiControllerTest extends TestCase
{
    use DatabaseTransactions, WithFaker;

    // @STORE
    public function test_it_can_create_a_new_customer()
    {
        // Given ...
        $form_data = [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'gender' => 'Male',
            'date_of_birth' => $this->faker->date(),
            'id_type' => 'Passport Number',
            'id_number' => $this->faker->ean13(),
            'classification' => 'Individual',
            'telephone_number' => $this->faker->unique()->e164PhoneNumber(),
            'email_address' => $this->faker->unique()->safeEmail(),
            'marital_status' => 'Married'
        ];

        // When ...
        $response = $this->postJson('/api/customers/create', $form_data);

        // Then ...
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['message' => 'Customer created successfully.']);

        $last_customer = Customer::latest('id')->first();

        $this->assertEquals($form_data['first_name'], $last_customer->First_Name);
        $this->assertEquals($form_data['last_name'], $last_customer->Last_Name);
        $this->assertEquals($form_data['date_of_birth'], $last_customer->Date_of_Birth->toDateString());
        $this->assertEquals($form_data['email_address'], $last_customer->Email_Address);
    }

    public function test_it_does_not_create_customer_when_customer_with_phone_number_already_exists()
    {
        // Given ...
        $existing_customer = Customer::factory()->create();

        $form_data = [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'gender' => 'Male',
            'date_of_birth' => $this->faker->date(),
            'id_type' => 'Passport Number',
            'id_number' => $this->faker->ean13(),
            'classification' => 'Individual',
            'telephone_number' => $existing_customer->Telephone_Number,
            'email_address' => $this->faker->unique()->safeEmail(),
            'marital_status' => 'Married'
        ];

        // When ...
        $response = $this->postJson('/api/customers/create', $form_data);

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Customer already exists.']);
        $this->assertEquals($existing_customer->id, Customer::latest('id')->first()->id);
    }

    // @UPDATE
    public function test_can_update_existing_customer()
    {
        // Given ...
        $customer = Customer::factory()->create();

        $form_data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'gender' => $customer->Gender,
            'telephone_number' => $customer->Telephone_Number,
            'email_address' => $this->faker->unique()->safeEmail(),
            'date_of_birth' => $customer->Date_of_Birth,
            'id_type' => $customer->ID_Type,
            'id_number' => $customer->ID_Number,
            'classification' => 'Individual',
        ];

        // When ...
        $response = $this->putJson('/api/customers/update', $form_data);

        // Then ...
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['message' => 'Customer updated successfully.']);

        $customer->refresh();
        $this->assertEquals($form_data['first_name'], $customer->First_Name);
        $this->assertEquals($form_data['last_name'], $customer->Last_Name);
    }

    public function test_does_not_update_customer_when_phone_number_is_not_given()
    {
        // Given ...
        $customer = Customer::factory()->create();

        $form_data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
        ];

        // When ...
        $response = $this->putJson('/api/customers/update', $form_data);

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Customer phone number is required.']);

        $customer->refresh();
        $this->assertNotEquals($form_data['first_name'], $customer->First_Name);
        $this->assertNotEquals($form_data['last_name'], $customer->Last_Name);
    }

    public function test_does_not_update_customer_when_phone_number_given_does_not_belong_to_any_customer()
    {
        $form_data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'telephone_number' => $this->faker->unique()->e164PhoneNumber(),
        ];

        // When ...
        $response = $this->putJson('/api/customers/update', $form_data);

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Customer account not found.']);
    }

    // @DESTROY
    public function test_can_delete_existing_customer()
    {
        // Given ...
        $customer = Customer::factory()->create();

        // When ...
        $response = $this->deleteJson('/api/customers/delete', [
            'phone' => $customer->Telephone_Number
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['message' => 'Customer deleted successfully.']);

        $customer->refresh();
        $this->assertSoftDeleted($customer);
    }

    public function test_deletion_fails_gracefully_when_phone_number_is_not_given()
    {
        // When ...
        $response = $this->deleteJson('/api/customers/delete');

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'The phone field is required.']);
    }

    public function test_deletion_fails_gracefully_when_phone_number_given_does_not_belong_to_any_customer()
    {
        // When ...
        $response = $this->deleteJson('/api/customers/delete', [
            'phone' => $this->faker->unique()->e164PhoneNumber()
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Customer account not found.']);
    }

    // @RESTORE
    public function test_can_restore_deleted_customer()
    {
        // Given ...
        $customer = Customer::factory()->create();
        $customer->delete();

        // When ...
        $response = $this->putJson('/api/customers/restore', [
            'phone' => $customer->Telephone_Number
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['message' => 'Customer restored successfully.']);

        $customer->refresh();
        $this->assertNotSoftDeleted($customer);
    }

    public function test_restoration_fails_gracefully_when_phone_number_is_not_given()
    {
        // When ...
        $response = $this->putJson('/api/customers/restore');

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'The phone field is required.']);
    }

    public function test_restoration_fails_gracefully_when_phone_number_given_does_not_belong_to_any_customer()
    {
        // When ...
        $response = $this->putJson('/api/customers/restore', [
            'phone' => $this->faker->unique()->e164PhoneNumber()
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Customer account not found.']);
    }

    // @BAN
    public function test_can_ban_customer()
    {
        // Given ...
        $customer = Customer::factory()->create();

        $this->assertEquals(0, $customer->IS_Barned);

        // When ...
        $response = $this->putJson('/api/customers/barn', [
            'phone' => $customer->Telephone_Number,
            'reason' => 'Lorem ipsum'
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['message' => 'Customer barned successfully.']);

        $customer->refresh();
        $this->assertEquals(1, $customer->IS_Barned);
        $this->assertEquals('Lorem ipsum', $customer->Barning_Reason);
    }

    public function test_barning_a_customer_fails_gracefully_when_phone_number_is_not_given()
    {
        // When ...
        $response = $this->putJson('/api/customers/barn');

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'The phone field is required.']);
    }

    public function test_barning_a_customer_fails_gracefully_when_phone_number_given_does_not_belong_to_any_customer()
    {
        // When ...
        $response = $this->putJson('/api/customers/barn', [
            'phone' => $this->faker->unique()->e164PhoneNumber()
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Customer account not found.']);
    }

    // @UNBAN
    public function test_can_unban_customer()
    {
        // Given ...
        $customer = Customer::factory()->create();
        $customer->barn();

        $this->assertEquals(1, $customer->fresh()->IS_Barned);

        // When ...
        $response = $this->putJson('/api/customers/unbarn', [
            'phone' => $customer->Telephone_Number,
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['message' => 'Customer unbarned successfully.']);

        $customer->refresh();
        $this->assertEquals(0, $customer->IS_Barned);
    }

    public function test_unbarning_a_customer_fails_gracefully_when_phone_number_is_not_given()
    {
        // When ...
        $response = $this->putJson('/api/customers/unbarn');

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'The phone field is required.']);
    }

    public function test_unbarning_a_customer_fails_gracefully_when_phone_number_given_does_not_belong_to_any_customer()
    {
        // When ...
        $response = $this->putJson('/api/customers/unbarn', [
            'phone' => $this->faker->unique()->e164PhoneNumber()
        ]);

        // Then ...
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Customer account not found.']);
    }
}
