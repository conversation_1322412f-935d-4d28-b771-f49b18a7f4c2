<?php

namespace App\Listeners;

use App\Models\Partner;
use App\Models\Transaction;
use App\Events\DownPaymentReceived;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendDownPaymentReceivedEmail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(DownPaymentReceived $event): void
    {
        if (app()->isProduction() == false) {
            return;
        }
        // Send email to partners (Spiro cc DFCU)
        $partner = $event->loanApplication->partner;
        $transaction = Transaction::query()->firstWhere('Loan_Application_ID', $event->loanApplication->id);
        // todo: Attach partner to 
        $assetPartner = Partner::query()->firstWhere('Institution_Name', 'like', '%' . $transaction?->Asset_Provider . '%');

        if ($assetPartner?->Email_Notification_Recipients) {
            $mail = Mail::to(explode(',', $assetPartner->Email_Notification_Recipients));

            if (! empty($partner->Email_Notification_Recipients)) {
                $mail->cc(explode(',', $partner->Email_Notification_Recipients));
            }

            $mail->send(new \App\Mail\DownPaymentReceived($event->loanApplication));

            return;
        }

        if (! empty($partner->Email_Notification_Recipients)) {
            Mail::to(explode(',', $partner->Email_Notification_Recipients))
                ->send(new \App\Mail\DownPaymentReceived($event->loanApplication));
        }
    }
}
