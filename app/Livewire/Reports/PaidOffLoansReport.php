<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetPaidOffLoansReportDetailsAction;
use App\Traits\ExportsData;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\PaidOffLoansExport;

class PaidOffLoansReport extends Component
{
    use WithPagination, ExportsData;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.paid-off-loans-report', [
            'loans' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return redirect()->route('export.paid-off-loans.pdf', $this->getFilters());
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new PaidOffLoansExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData(): \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Support\Collection
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetPaidOffLoansReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }
}
