<?php

namespace App\Imports;

use App\Models\CashSale;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Facades\Auth;

class CashSalesImport implements ToModel, WithHeadingRow
{
    public function model(array $row)
    {
        return new CashSale([
            'receipt_number' => $this->generateReceiptNumber(),
            'customer_name' => $row['customer_name'],
            'customer_phone' => $this->formatPhoneNumber($row['customer_phone']),
            'customer_location' => $row['customer_location'],
            'amount' => $row['amount'],
            'partner_id' => Auth::user()->partner_id,
            'vin_no' => $row['vin_no'] ?? null,
            'reg_no' => $row['reg_no'] ?? null,
            'tin' => $row['tin'] ?? null,
            'usage' => $row['usage'] ?? null,
            'sales_executive' => $row['sales_executive'] ?? null,
            'lead_source' => $row['lead_source'] ?? null,
            'financier' => $row['financier'] ?? null,
        ]);
    }

    protected function generateReceiptNumber()
    {
        return 'CS-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
    }

    protected function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Ensure Kenyan numbers start with 7 or 1
        if (strlen($phone) === 9 && in_array($phone[0], ['1', '7'])) {
            return '0' . $phone;
        }

        return $phone;
    }
}
