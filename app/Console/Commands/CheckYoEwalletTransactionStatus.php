<?php

namespace App\Console\Commands;

use App\Models\Transaction;
use App\Notifications\SmsNotification;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckYoEwalletTransactionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:yo:check-ewallet-transaction-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the yo telco transaction status and complete transaction if its completed.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        $transactions = Transaction::whereIn("status", ["pending", "Pending", "PENDING"])
            ->whereNotNull('Provider_TXN_ID')
            ->orderBy('id', 'desc')
            ->orderBy('Retry_Count')
            ->get();
        foreach ($transactions as $transaction) {
            try {
                $payment = (new PaymentServiceManager($transaction))->paymentService;

                if (strtolower($transaction->Type) === strtolower(Transaction::DISBURSEMENT)) {
                    $response = $payment->disbursementStatus($transaction->Provider_TXN_ID);
                } else {
                    $response = $payment->collectionStatus($transaction->Provider_TXN_ID);
                }

                $transactionStatusFromProvider = strtoupper(Arr::get($response, 'message'));
                $transaction->Narration = Arr::get($response, 'payment_message');

                if ($transactionStatusFromProvider === $payment->getStatusSuccessMessage()) {
                    $result = $payment->processCallback($transaction);
                    if ($result) {
                        $transaction->Status = 'Completed';
                        $transaction->Payment_Reference = Arr::get($response, 'payment_reference');

                        if ($transaction->Type == Transaction::DOWNPAYMENT) {
                            $transaction->Asset_Disbursement_Status = 'Pending';
                        }
                    }
                } else if ($transactionStatusFromProvider === 'FAILED') {
                    $transaction->Status = 'Failed';
                } else {
                    if ($transaction->Retry_Count <= 5) {
                        $transaction->increment('Retry_Count');
                    } else {
                        $transaction->Status = 'Failed';
                        if ($transaction->Type == Transaction::DISBURSEMENT) {
                            $transaction->loanApplication->update([
                                'Credit_Application_Status' => 'Rejected',
                                'Rejection_Reason' => $transaction->Narration,
                                'Rejection_Date' => date('Y-m-d'),
                                'Last_Status_Change_Date' => date('Y-m-d'),
                            ]);
                            //send message to customer that transaction failed
                            $transaction->customer->notify(new SmsNotification('Unfortunately, your loan application of UGX ' . number_format($transaction->Amount)  . ' from ' . $transaction->partner->Institution_Name . ' was unsuccessful. Please try again.', $transaction->Telephone_Number, $transaction->customer->id, $transaction->partner->id));
                            // todo: Dispatch SMS to user about this loan application
                        }
                    }
                }
                $transaction->save();
                DB::commit();
            } catch (\Throwable $throwable) {
                DB::rollBack();
                Log::error($throwable->getMessage(), [
                    "line" => $throwable->getLine(),
                    "trace" => $throwable->getTrace()
                ]);
            }
        }
        return 0;
    }
}
