<?php

namespace App\Console\Commands;

use App\Models\JournalEntry;
use App\Models\Loan;
use App\Models\LoanDisbursement;
use App\Models\LoanRepayment;
use App\Models\Partner;
use App\Models\Transaction;
use App\Services\Account\AccountSeederService;
use App\Services\FinacleService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PostCollectionToPartner extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:post-collection-to-partner';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $partner = Partner::where('Identification_Code', 'CI009')->firstOrFail();
        $repayments = LoanRepayment::where('partner_id', $partner->id)->where('partner_notified', false)->get();
        foreach ($repayments as $repayment) {
            $loanProduct = $repayment->loan->loan_product;
            $journalEntries = JournalEntry::where('transactable_id', $repayment->id)->get();
            $interestIncome = $journalEntries->where('account_name', AccountSeederService::INTEREST_INCOME_FROM_LOANS_NAME)->first()->amount ?? 0;
            $normalInterest = $journalEntries->where('account_name', AccountSeederService::INTEREST_RECEIVABLES_NAME)->first()->amount ?? 0;
            $penalInterest = $journalEntries->where('account_name', AccountSeederService::PENALTIES_RECEIVABLES_NAME)->first()->amount ?? 0;
            $principalAmount = $journalEntries->where('account_name', $loanProduct->Name)->first()->amount ?? 0;
            $applicationId = $repayment->loan->application->id;
            $transaction = Transaction::where('Type', 'Repayment')->where('Loan_Application_ID', $applicationId)->latest()->first();
            $result = $this->postCollectionToPartner($principalAmount, $normalInterest, $interestIncome, $penalInterest,  $repayment->customer->Telephone_Number, $repayment->customer->name, $repayment->Loan_ID, $transaction->Payment_Reference);
            $this->info(json_encode($result));
            if (isset($result['response']) && strpos($result['response'], '<ERROR>') !== false) {
                preg_match('/<ERROR>(.*?)<\/ERROR>/', $result['response'], $matches);
                $errorMessage = isset($matches[1]) ? $matches[1] : 'Unknown error';
                Log::error('post-collection-to-partner error: ' . $errorMessage);
                $this->error('Error in response: ' . $errorMessage);
                // replace this line above with the actual error
                continue;
            }
            if ($result['success']) {
                $repayment->partner_notified = true;
                $repayment->partner_notified_date = Carbon::now();
                $repayment->save();
                $this->info('Partner has been notified of this loan repayment');
            }
        }
    }

    // todo: Handle posting to ABC in a job to avoid blocking the request.
    private function postCollectionToPartner($principalAmount, $normalInterest, $interestIncome, $penalInterest, $phone, $name, $loanId, $transactionReference)
    {
        $finacleService = new FinacleService();
        $result = $finacleService->executeLoanCollection(
            round($principalAmount),
            round($normalInterest),
            round($interestIncome),
            round($penalInterest),
            $phone,
            $name,
            Carbon::now()->format('Y-m-d\TH:i:s.v'),
            $loanId,
            $transactionReference
        );
        return $result;
    }
}
