<?php

use App\Console\Commands\AccrueDailyLoanInterest;
use App\Console\Commands\ApplyDfcuLoanPenalties;
use App\Console\Commands\ApplyLoanPenalties;
use App\Console\Commands\AutoSweep;
use App\Console\Commands\AutoWriteOffAfterDays;
use App\Console\Commands\CheckYoEwalletTransactionStatus;
use App\Console\Commands\FlagOverdueLoans;
use App\Console\Commands\ImmobilizeBikes;
use App\Console\Commands\MarkLoansAsRestructurable;
use App\Console\Commands\MobilizeBikes;
use App\Console\Commands\PastDueLoanReminders;
use App\Console\Commands\PostCollectionToPartner;
use App\Console\Commands\PostDisbursementToPartner;
use App\Console\Commands\PostNormalInterest;
use App\Console\Commands\PostPenalInterest;
use App\Console\Commands\RemindBorrowers;
use App\Console\Commands\SendLoggedSms;
use App\Console\Commands\Spiro\GenerateLeads;
use Illuminate\Support\Facades\Schedule;

// Check transactions status and clear it.
Schedule::command(CheckYoEwalletTransactionStatus::class)->everyMinute()->withoutOverlapping();
Schedule::command(SendLoggedSms::class)->everyMinute()->withoutOverlapping();
Schedule::command(RemindBorrowers::class)->dailyAt("8pm");
Schedule::command(ImmobilizeBikes::class)->daily();
Schedule::command(ApplyLoanPenalties::class)->dailyAt('11pm');
Schedule::command('lms:get-ussd-hits --partner=FSP016')->everyFiveMinutes()->withoutOverlapping();
Schedule::command('lms:get-ussd-hits --partner=CB009')->everyThreeHours()->withoutOverlapping();
// Schedule::command(MobilizeBikes::class)->everyFiveMinutes();
Schedule::command(MarkLoansAsRestructurable::class)->everyTwoMinutes();
// Schedule::command(RefreshTokens::class)->everyThirtyMinutes();
// Schedule::command(PostCollectionToPartner::class)->everyFiveMinutes();
// ->dailyAt('5am')->withoutOverlapping();
// Schedule::command(PostDisbursementToPartner::class)->everyThreeMinutes();
// Schedule::command(PostPenalInterest::class)->daily("23:55")->withoutOverlapping();
// Schedule::command(PostNormalInterest::class)->dailyAt("23:58")->withoutOverlapping();
// ->dailyAt('4am')->withoutOverlapping();
Schedule::command(FlagOverdueLoans::class)->dailyAt("6am");
Schedule::command(PastDueLoanReminders::class)->dailyAt("9am");
Schedule::command(AutoWriteOffAfterDays::class)->dailyAt("09:10");
Schedule::command(GenerateLeads::class)->dailyAt("00:10");
Schedule::command(AccrueDailyLoanInterest::class)->dailyAt("00:01");
Schedule::command(ApplyDfcuLoanPenalties::class)->dailyAt("23:59");
Schedule::command(AutoSweep::class)->dailyAt("01:00");
