@extends('excel.layouts')

@section('content')
    <table id="report-table" class="table table-bordered">
        <thead>
            <tr>
                <th colspan="6" style="font-size: 20px; text-align: center; padding: 2px;">{{ $partnerName }}</th>
            </tr>
            <tr>
                <th colspan="6" style="font-weight: bold; font-size: 16px; text-align: center; padding: 2px;">Interest
                    Receivable Report</th>
            </tr>
            <tr>
                <th colspan="6" style=" font-size: 14px; text-align: center; padding: 2px;">From:
                    {{ $filters['startDate'] }} to {{ $filters['endDate'] }}</th>
            </tr>
            <tr>
                <th colspan="6"></th>
            </tr>
            <tr>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Loan #</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Customer</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Phone Number</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Loan Amount</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Interest</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Interest Receivable</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Interest Paid</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Accrued Interest</th>
            </tr>
        </thead>
        <tbody class="">
            @forelse ($records as $record)
                <tr>
                    <td style="text-align: left">{{ $record->id }}</td>
                    <td>{{ $record->customer->name }}</td>
                    <td style="text-align: right;">{{ $record->customer->Telephone_Number }}</td>
                    <td style="text-align: right;">{{ number_format($record->Credit_Amount) }}</td>
                    <td style="text-align: right;">{{ number_format($record->schedule_sum_interest) }}</td>
                    <td style="text-align: right;">{{ number_format($record->interest_receivable) }}</td>
                    <td style="text-align: right;">{{ number_format($record->interest_paid) }}</td>
                    <td style="text-align: right;">
                        {{ number_format($record->interest_receivable - $record->interest_paid) }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6">No records found</td>
                </tr>
            @endforelse
        </tbody>
        <tfoot class="fw-bold">
            <tr>
                <th style="border: 1px solid black; padding: 2px; font-weight: bold;">Totals</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">
                    {{ $records->count() }}</th>
                <th colspan="" style="border: 1px solid black; padding: 2px; font-weight: bold;"></th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">
                    {{ number_format($records->sum('Credit_Amount')) }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">
                    {{ number_format($records->sum('schedule_sum_interest')) }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">
                    {{ number_format($records->sum('interest_receivable')) }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">
                    {{ number_format($records->sum('interest_paid')) }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">
                    {{ number_format($records->sum('interest_receivable') - $records->sum('interest_paid')) }}</th>
            </tr>
        </tfoot>
    </table>
    <x-print-footer />
@endsection
