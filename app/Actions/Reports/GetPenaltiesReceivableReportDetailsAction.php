<?php

namespace App\Actions\Reports;

use App\Models\Loan;
use App\Services\Account\AccountSeederService;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GetPenaltiesReceivableReportDetailsAction
{
    protected string $startDate = '';
    protected string $endDate = '';
    protected int $perPage = 0;

    public function execute()
    {

        $query = Loan::query()
            ->with(['customer'])
            ->withSum('penalties', 'Amount_To_Pay')
            ->whereNotIn('Credit_Account_Status', [
                Loan::ACCOUNT_STATUS_WRITTEN_OFF,
                Loan::ACCOUNT_STATUS_FULLY_PAID_OFF
            ]);

        $query->latest();

        if ($this->perPage > 0) {
            return $query->paginate($this->perPage);
        }
        return $query->get();
    }

    public function paginate($perPage = 100): self
    {
        $this->perPage = $perPage;

        return $this;
    }

    public function filters(array $details): self
    {
        $this->startDate = Arr::get($details, 'startDate', now()->toDateString());
        $this->endDate = Arr::get($details, 'endDate', now()->toDateString());

        if (empty($this->startDate)) {
            $this->startDate = now()->toDateString();
        }

        if (empty($this->endDate)) {
            $this->endDate = now()->toDateString();
        }

        if (Carbon::parse($this->endDate)->isFuture()) {
            $this->endDate = now()->toDateString();
        }

        return $this;
    }
}
