<?php

namespace App\Actions\Loans;

use App\Actions\MarkLoanAsRestructuredAction;
use App\Models\Loan;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessLoanRestructureAction
{
    public function execute(Transaction $transaction): bool
    {
        $loan = $transaction->loan;
        $days = $transaction->Restructure_Days;
        $days = (int) $days;

        try {
            DB::beginTransaction();

            if ($loan->Status == Loan::WRITTEN_OFF_STATUS) {
                throw new \Exception('You cannot make a repayment on a written-off loan.', 400);
            }

            // Fetch the penalty fee
            $penaltyFee = $loan->loan_product->penalties()->first();
            if (!$penaltyFee) {
                throw new \Exception('Product does not have a penalty fee.', 400);
            }

            // Step 1: Get amount due (arrears)
            $arrears = $loan->getAmountDue();
            if ($arrears <= 0) {
                throw new \Exception('There are no arrears for this loan to be restructured.', 400);
            }
            $arrears = round($arrears, 2); // Ensure arrears is rounded to 2 decimal places

            // Step 2: Calculate total restructured amount (arrears + penalty)
            $monthlyPenaltyRate = $penaltyFee->Value / 100; // Penalty rate (e.g., 3.5%)
            $dailyPenaltyRate = $monthlyPenaltyRate / 30; // Convert to daily rate
            $penaltyRate = $dailyPenaltyRate * $days; // Total penalty for the restructure period
            $penalty = $arrears * $penaltyRate;
            $totalRestructuredAmount = $arrears + $penalty;
            $totalRestructuredAmount -= $transaction->Amount; // Subtract the transaction amount

            // Step 3: Use $days to calculate the restructured amount per installment
            $additionalAmountPerInstallment = $totalRestructuredAmount / $days;

            // Step 4: Fetch the existing repayment schedule starting from today
            $existingSchedule = $loan->schedule()
                ->where('total_outstanding', '>', 0) // Only outstanding payments
                ->whereBetween('payment_due_date', [
                    now()->addDays(1)->startOfDay()->toDateTimeString(),
                    now()->addDays($days)->endOfDay()->toDateTimeString()
                ]) // Start from today
                ->orderBy('payment_due_date') // Order by due date
                ->get();

            if ($existingSchedule->isEmpty()) {
                throw new \Exception("No repayments with outstanding amounts found starting from today.");
            }

            // Step 5: Group the schedule by installment_number
            $groupedSchedule = $existingSchedule->groupBy('installment_number');

            // Step 6: Process each grouped schedule
            foreach ($groupedSchedule as $installmentNumber => $installmentPayments) {
                // Calculate the total payment for the installment
                $totalInstallmentPayment = $installmentPayments->sum('total_payment');
                // Step 7: Spread the additional amount across the payment types
                foreach ($installmentPayments as $payment) {
                    // Calculate the percentage of this payment type in the installment
                    $paymentPercentage = $payment->total_payment / $totalInstallmentPayment;

                    // Calculate the additional amount for this payment type
                    $additionalAmount = $additionalAmountPerInstallment * $paymentPercentage;

                    // Calculate the percentage of principal and interest in this payment
                    $principalPercentage = $payment->principal / $payment->total_payment;
                    $interestPercentage = $payment->interest / $payment->total_payment;

                    // Calculate the additional principal and interest
                    $additionalPrincipal = $additionalAmount * $principalPercentage;
                    $additionalInterest = $additionalAmount * $interestPercentage;

                    // Update the payment amounts
                    $payment->principal += $additionalPrincipal;
                    $payment->principal_remaining += $additionalPrincipal;

                    $payment->interest += $additionalInterest;
                    $payment->interest_remaining += $additionalInterest;

                    $payment->total_payment += $additionalAmount;
                    $payment->total_outstanding += $additionalAmount;

                    // Save the updated payment record
                    $payment->save();
                }
            }

            // Step 8: Clear previous installments
            $loan->schedule()
                ->where('payment_due_date', '<=', now()->toDateString()) // Installments before today
                ->update([
                    'interest_remaining' => 0,
                    'principal_remaining' => 0,
                    'total_outstanding' => 0,
                ]);
            // Return success response
            app(MarkLoanAsRestructuredAction::class)->execute($loan);
            DB::commit();
            return true;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            DB::rollBack();
            return false;
        }
    }
}
