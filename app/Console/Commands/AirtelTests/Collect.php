<?php

namespace App\Console\Commands\AirtelTests;

use App\Actions\CreateTestTransactionAction;
use App\Models\PartnerOva;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

/**
 * Use this when in UATs for Airtel
 */
class Collect extends Command
{
  protected $signature = 'airtel:collect {--phone=} {--amount=} {--partner=}';
  protected $description = 'Collect money from customer use: 752600157 for testing';

  public function handle(): int
  {
    try {
      $transaction = app(CreateTestTransactionAction::class)->execute(
        $this->option('amount'),
        $this->option('phone'),
        $this->option('partner')
      );
      $id = Str::random(10);

      $payload = [
          "reference" => $transaction->Type,
          "subscriber" => [
              "country" => "UG",
              "currency" => "UGX",
              "msisdn" => $this->option('phone')
          ],
          "transaction" => [
              "amount" => $this->option('amount'),
              "country" => "UG",
              "currency" => "UGX",
              "id" => $id
          ]
      ];
      $this->info('Payload');
      $this->info(json_encode($payload, true));

      $api = (new PaymentServiceManager($transaction))->paymentService;

      $response = $api->collect($transaction->Telephone_Number, $transaction->Amount, $transaction->TXN_ID, $transaction->Type);

      $this->info(json_encode($response, true));

      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
