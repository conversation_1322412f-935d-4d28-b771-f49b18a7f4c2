<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Switches;
use App\Notifications\SmsNotification;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Sms
{
    protected ?Switches $switch = null;  // Made nullable

    protected $sms_config;

    protected $url;
    protected $name;
    protected $password;
    protected $senderId;

    public function __construct(?SmsNotification $notification = null)
    {
        $query = Switches::where('category', 'SMS')
            ->where('status', 'On');

        // Look for partner-specific switch if notification has partnerID
        if ($notification?->partnerID) {
            $this->switch = $query->clone()
                ->where('partner_id', $notification->partnerID)
                ->first();
        }

        // Fall back to general switch if no partner-specific found
        if (!isset($this->switch)) {
            $this->switch = $query->clone()
                ->whereNull('partner_id')
                ->first();
        }

        if ($this->switch) {
            $this->configureProvider();
        }
    }
    protected function configureProvider()
    {
        $this->sms_config = config('services.sms');

        if ($this->switch->name == 'DMARK') {
            if ($this->switch->environment == 'Production') {
                $this->url = $this->sms_config['DMARK_PROD']['url'];
                $this->name = $this->sms_config['DMARK_PROD']['spname'];
                $this->password = $this->sms_config['DMARK_PROD']['sppass'];
            } else {
                $this->url = $this->sms_config['DMARK_TEST']['url'];
                $this->name = $this->sms_config['DMARK_TEST']['spname'];
                $this->password = $this->sms_config['DMARK_TEST']['sppass'];
            }
        } else if ($this->switch->name == 'AFRICASTALKING') {
            if ($this->switch->environment == 'Production') {
                $this->senderId = $this->switch->sender_id ?? $this->sms_config['AFRICASTALKING_PROD']['sender_id'];
                $this->url = $this->sms_config['AFRICASTALKING_PROD']['url'];
                if ($this->switch->partner_id && $this->switch->username && $this->switch->password) {
                    $this->name = $this->switch->username;
                    $this->password = $this->switch->password;
                } else {
                    $this->name = $this->sms_config['AFRICASTALKING_PROD']['spname'];
                    $this->password = $this->sms_config['AFRICASTALKING_PROD']['sppass'];
                }
            } else {
                $this->senderId = $this->switch->sender_id ?? $this->sms_config['AFRICASTALKING_TEST']['sender_id'];
                $this->url = $this->sms_config['AFRICASTALKING_TEST']['url'];
                $this->name = $this->sms_config['AFRICASTALKING_TEST']['spname'];
                $this->password = $this->sms_config['AFRICASTALKING_TEST']['sppass'];
            }
        }
    }

    /**
     * @throws Exception
     */
    public function send($notifiable, SmsNotification $notification): void
    {
        $message = $notification->toSms($notifiable);

        // Check if this is an old notification that should be skipped
        if ($message === '__SKIP_OLD_NOTIFICATION__') {
            return;
        }

        $this->sendSms($notifiable->routeNotificationForSms(), $message);
    }

    public function sendSms($phone, $message): bool
    {
        $data = [
            'username' => ($this->name),
            'from' => $this->senderId,
            'password' => ($this->password),
        ];

        if (empty($this->switch)) {
            return false;
        }

        if (app()->isLocal()) {
            return true;
        }

        $response = null;

        if ($this->switch->name == 'DMARK') {
            $response = Http::get($this->url, [
                'spname' => $this->name,
                'sppass' => $this->password,
                'type'   => 'json',
                'msg'    => $message,
                'numbers' => $phone,
            ]);
        }

        if ($this->switch->name == 'AFRICASTALKING') {
            $response = Http::withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded',
                'apiKey' => $this->password
            ])->asForm()->post($this->url, [
                'username' => ($this->name),
                'from' => $this->senderId,
                'to' => ($phone), // Phone number(s)
                'message' => ($message) // Custom message
            ]);
        }

        if ($response->successful()) {
            return true;
        }

        // Optionally, you can throw an exception or handle errors here
        throw new Exception('Failed to send SMS: ' . $response->body());
    }
}
