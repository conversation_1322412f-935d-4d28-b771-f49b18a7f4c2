<?php

namespace App\Livewire\OtherReports;

use App\Actions\OtherReports\GetDailyReconciliationReportDetailsAction;
use App\Actions\Reports\GetPerformanceMetricsReportDetailsAction;
use App\Exports\PerformanceMetricsExport;
use App\Services\Account\AccountSeederService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class PerformanceMetricsReport extends Component
{
    use WithPagination;

    public string $startDate = '';
    public string $endDate = '';
    public string $accountType = AccountSeederService::COLLECTION_OVA_SLUG;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->toDateString();
        $this->endDate = now()->toDateString();
    }

    public function render()
    {
        return view('livewire.reports.performance-metrics', $this->getViewData());
    }

    public function printReport()
    {
        // Format data for the report
        $filters = $this->getFilters();
        $filters['startDate'] = Carbon::parse($this->startDate)->format('d-m-Y');
        $filters['endDate'] = Carbon::parse($this->endDate)->format('d-m-Y');

        // Get data to pass to the report
        $viewData = $this->getViewData();
        $viewData['filters'] = $filters;
        $viewData['partner'] = auth()->user()->partner;

        $pdf = Pdf::loadView('pdf.performance-metrics', $viewData)->setPaper('A4', 'landscape');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, 'performance-metrics-report-'.$this->endDate.'.pdf');
    }

    public function excelExport()
    {
        return Excel::download(new PerformanceMetricsExport($this->getFilters()), 'performance-metrics-report-'.now()->toDateString().'.xlsx');
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetPerformanceMetricsReportDetailsAction::class)
            ->filters($this->getFilters())
            ->execute();
    }

    protected function getFilters()
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    protected function getViewData()
    {
        $records = $this->getReportData();

        return [
            'records' => $records
        ];
    }
}
