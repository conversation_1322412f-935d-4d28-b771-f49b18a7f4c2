<?php

namespace App\Models;

use App\Models\Accounts\Account;
use App\Models\Partner;
use App\Models\Customer;
use App\Models\LoanSchedule;
use App\Models\Scopes\PartnerScope;
use App\Models\LoanProductPenalties;
use App\Models\Transactables\Contracts\Transactable;
use App\Services\Account\AccountSeederService;
use App\Traits\AccountOperations;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class LoanPenalty extends Model implements Transactable
{
    use HasFactory, SoftDeletes;
    use AccountOperations;

    const FULLY_PAID = 'Fully Paid';
    const NOT_PAID = 'Not Paid';
    const PARTIALLY_PAID = 'Partially Paid';

    protected $fillable = [
        "Partner_ID",
        "Loan_ID",
        "Product_Penalty_ID",
        "Customer_ID",
        "Amount",
        "Amount_To_Pay",
        "Status",
    ];

    public static function boot()
    {
        parent::boot();
        static::addGlobalScope(new PartnerScope);
        static::created(function ($penalty) {
            if ($penalty->partner->Accounting_Type == 'Accrual') {
                $penalty->affectAccounts();
            }
        });
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, "Customer_ID");
    }

    public function loan()
    {
        return $this->belongsTo(Loan::class, "Loan_ID");
    }

    public function loan_product_penalty()
    {
        return $this->belongsTo(LoanProductPenalties::class, "Product_Penalty_ID");
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class, "Partner_ID");
    }

    public function amount(): float
    {
        return $this->Amount_To_Pay;
    }
    public function affectAccounts()
    {
        try {
            // Get the penalties receivables account
            $penaltiesReceivablesAccount = Account::where("partner_id", $this->Partner_ID)
                ->where("slug", AccountSeederService::PENALTIES_RECEIVABLES_SLUG)
                ->first();
            if (!$penaltiesReceivablesAccount) {
                $penaltiesReceivablesAccount = $this->addPenaltiesReceivableAccount($this->Partner_ID);
            }

            // Get the penalties income account
            $incomeFromPenaltiesAccount = Account::where("partner_id", $this->Partner_ID)
                ->where("slug", AccountSeederService::PENALTIES_FROM_LOAN_PAYMENTS_SLUG)
                ->first();
            $amount = $this->amount();
            // Update account balances and create transactions
            $penaltiesReceivablesAccount->increment('balance', $amount);
            $incomeFromPenaltiesAccount->increment('balance', $amount);
            $txnId = $this->generateTransactionId();
            // Create transactions
            JournalEntry::create([
                'customer_id' => $this->Customer_ID,
                'account_id' => $penaltiesReceivablesAccount->id,
                'amount' => $amount,
                'transactable_id' => $this->id,
                'transactable' => LoanPenalty::class,
                'partner_id' => $this->Partner_ID,
                'txn_id' => $txnId,
                'account_name' => $penaltiesReceivablesAccount->name,
                'cash_type' => 'Cash In',
                'previous_balance' => $penaltiesReceivablesAccount->balance - $amount,
                'current_balance' => $penaltiesReceivablesAccount->balance,
                'accounting_type' => 'Debit',
                'credit_amount' => 0,
                'debit_amount' => $amount,
            ]);

            JournalEntry::create([
                'customer_id' => $this->Customer_ID,
                'account_id' => $incomeFromPenaltiesAccount->id,
                'amount' => $amount,
                'transactable_id' => $this->id,
                'transactable' => LoanPenalty::class,
                'partner_id' => $this->Partner_ID,
                'txn_id' => $txnId,
                'account_name' => $incomeFromPenaltiesAccount->name,
                'cash_type' => 'Cash In',
                'previous_balance' => $incomeFromPenaltiesAccount->balance - $amount,
                'current_balance' => $incomeFromPenaltiesAccount->balance,
                'accounting_type' => 'Credit',
                'credit_amount' => $amount,
                'debit_amount' => 0,
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }
    }

    /**
     * Generate a transaction ID
     */
    private function generateTransactionId(): string
    {
        return rand(11111, 99999) . "-" . now()->unix();
    }

    public function journalEntries(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(JournalEntry::class, 'journable', 'transactable', 'transactable_id');
    }
}
