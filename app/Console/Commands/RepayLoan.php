<?php

namespace App\Console\Commands;

use App\Models\Accounts\Account;
use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Services\Account\AccountSeederService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepayLoan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:repay-loan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        DB::beginTransaction();
        $loan = Loan::latest()->first();
        try {
            $loan_payment = LoanRepayment::createPayment($loan, 116600);
            $loan_payment->affectAccounts();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            //dd($e->getMessage());
        }
    }
}
