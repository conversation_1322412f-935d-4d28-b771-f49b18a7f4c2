<?php

namespace App\Http\Controllers\Api;

use Exception;
use App\Models\Partner;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Models\SavingsAccount;
use App\Models\SavingsProduct;
use App\Services\SavingsService;
use App\Jobs\SendSMSNotification;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\SavingsProductApiResource;
use App\Http\Resources\SavingsTransactionsApiResource;
use App\Models\CustomerSavingsPreferences;
use App\Models\SavingsApplication;
use App\Models\SavingsProductFee;
use App\Notifications\SavingsAccountCreatedNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SavingsApiController extends Controller
{
    /**
     * Update the document status of the specified savings application.
     */
    public function updateStatus(Request $request)

    {
        try {
            // Validate the request data
            $validator = Validator::make($request->all(), [
                'Document_Status' => 'required|string|max:255',
                'Reference_Code' => 'required|string|max:255',
            ]);

            $status = $request->input('Document_Status');
            $code = $request->input('Reference_Code');
            // If validation fails, return error response
            if ($validator->fails()) {
                return response()->json([
                    'message' => $validator->errors()->first()
                ], 400);
            }

            // Find the savings application by ID
            $savingsApplication = SavingsApplication::where('Reference_Code', $code)->firstOrFail();

            // Update the document status
            $savingsApplication->Document_Status = $status;
            $savingsApplication->save();

            return response()->json([
                'message' => 'Document status updated successfully',
                'data' => $savingsApplication
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function postCustomerPreferences($Partner_ID, $Saving_Product_ID, $Customer_ID, $Payment_Frequency, $Payment_Type, $Default_Installment, $Expected_Amount, $Provider_Name = null, $Location = null)
    {
        try {
            DB::beginTransaction();
            // Example data (you can get this from the request or elsewhere)
            $data = [
                'Partner_ID' => $Partner_ID,
                'Saving_Product_ID' => $Saving_Product_ID,
                'Customer_ID' => $Customer_ID,
                'Provider_Name' => $Provider_Name,
                'Payment_Frequency' => $Payment_Frequency,
                'Payment_Type' => $Payment_Type,
                'Default_Installment' => $Default_Installment,
                'Expected_Amount' => $Expected_Amount,
                'Location' => $Location,
            ];

            // Validate the data
            $validator = Validator::make($data, [
                'Partner_ID' => 'required|exists:partners,id',
                'Saving_Product_ID' => 'required|exists:savings_products,id',
                'Customer_ID' => 'required|exists:customers,id',
                'Provider_Name' => 'nullable|string|max:255',
                'Payment_Frequency' => 'nullable|string|max:255',
                'Default_Installment' => 'nullable|numeric',
                'Payment_Type' => 'nullable|string|max:255',
                'Expected_Amount' => 'required|numeric',
                'Location' => 'required|string',
            ]);

            if ($validator->fails()) {
                Log::error($validator->errors());
            }
            $conditions = [
                'Partner_ID' => $data['Partner_ID'],
                'Saving_Product_ID' => $data['Saving_Product_ID'],
                'Customer_ID' => $data['Customer_ID'],
            ];
            CustomerSavingsPreferences::updateOrCreate($conditions, $data);
            DB::commit();
        } catch (Exception $e) {
            Log::error($e->getMessage());
            DB::rollBack();
        }
    }

    private function createSavingAccount(Customer $customer, SavingsProduct $savingsProduct, float $expectedAmount)
    {
        try {
            // Create the savings account
            $savingsAccount = new SavingsAccount();
            $savingsAccount->partner_id = $savingsProduct->partner->id;
            $savingsAccount->customer_id = $customer->id;
            $savingsAccount->savings_product_id = $savingsProduct->id;
            $savingsAccount->expected_amount = $expectedAmount;
            $savingsAccount->save();
            return $savingsAccount;
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }
    }

    public function deposit(Request $request)
    {
        try {
            // Validate the request data
            $validator = Validator::make($request->all(), [
                'phoneNumber' => 'required',
                'productCode' => 'required',
                'amount' => 'required',
                'fee_id' => 'required',
                'expectedAmount' => 'required',
                'providerName' => 'nullable|string',
                'paymentFrequency' => 'nullable|string',
                'paymentInstallment' => 'nullable|numeric',
                'paymentType' => 'nullable|string',
                'location' => 'nullable|string'
            ]);

            // If validation fails, return error response
            if ($validator->fails()) {
                $message = $validator->errors()->first();
                throw new Exception($message . ' Request:' . json_encode($request->all()));
            }
            $location = $request->location;
            $providerName = $request->providerName;
            $feeID = $request->fee_id;
            $paymentFrequency = $request->paymentFrequency;
            $paymentInstallment = $request->paymentInstallment;
            $paymentType = $request->paymentType;
            $expectedAmount = $request->expectedAmount;
            $productCode = $request->productCode;
            $phone = $request->phoneNumber;
            $amount = $request->amount;
            $partner_code = $request->header('X-PARTNER-CODE');
            $customer = Customer::where('Telephone_Number', $phone)->firstOrFail();
            $partner = Partner::where('Identification_Code', $partner_code)->firstOrFail();
            $product = SavingsProduct::where('code', $productCode)->where('Partner_ID', $partner->id)->firstOrFail();
            DB::beginTransaction();

            $application = SavingsApplication::where('Customer_ID', $customer->id)
                ->where('Status', 'Pending')
                ->whereIn('Category', ['loan', 'downpayment'])
                ->first();
            if ($application && ($paymentType == 'loan' || $paymentType == 'downpayment')) {
                throw new Exception('Customer already has a pending loan application.');
            }
            $savings_account = $customer->savings_account()->where('savings_product_id', $product->id)->first();
            if (!$savings_account) {
                $savings_account = $this->createSavingAccount($customer, $product, $expectedAmount);
            }
            $savings_account->expected_amount = $expectedAmount;
            $savings_account->save();
            $futureBalance = $savings_account->current_balance + $amount;
            if ($futureBalance > $expectedAmount) {
                throw new Exception('Customer cannot deposit more than the expected amount.');
            }
            // if ($paymentType == 'buy' || $paymentType == 'loan') {
            $this->postCustomerPreferences($partner->id, $product->id, $customer->id, $paymentFrequency, $paymentType, $paymentInstallment, $expectedAmount, $providerName, $location);
            // }
            // Throws exception if deposit fails

            $fee = SavingsProductFee::find($feeID);
            SavingsService::initiateDeposit($partner, $customer, $amount, $fee);
            DB::commit();
            return response()->json([
                "returnCode" => 511,
                "returnMessage" => "Transaction In Progress."
            ], 200);
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            DB::rollBack();
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    public function withdraw(Request $request)
    {
        $phone = $request->phone;
        $amount = $request->amount;
        $partner_code = $request->header('X-PARTNER-CODE');
        $customer = Customer::where('Telephone_Number', $phone)->first();
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        DB::beginTransaction();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }

            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $savings_account = $customer->savings_account;
            if (!$savings_account) {
                throw new Exception('Customer doesn\'t have a savings account.', 400);
            }

            // Throws exception if withdraw fails
            SavingsService::initiateWithdraw($partner, $customer, $amount);
            DB::commit();
            return response()->json([
                'message' => "Successfully initiated a withdrawal of UGX " . number_format($amount, 2),
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                'message' => 'Something went wrong with your withdrawal request. ' . $th->getMessage()
            ], 400);
        }
    }

    public function balance(Request $request)
    {
        $phone = $request->phone;
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }
            return response()->json([
                'balance' => $customer->savings_account->current_balance
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Something went wrong. ' . $th->getMessage()
            ], 400);
        }
    }

    public function statement(Request $request)
    {
        $phone = $request->phone;
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }
            $savings_account = $customer->savings_account;
            if (!$savings_account) {
                throw new Exception('Customer doesn\'t have a savings account.', 400);
            }
            $transactions = $savings_account->transactions();
            return response()->json([
                'statement' => SavingsTransactionsApiResource::collection($transactions)
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Something went wrong. ' . $th->getMessage()
            ], 400);
        }
    }

    public function about(Request $request)
    {
        $phone = $request->phone;
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }
            $savings_account = $customer->savings_account;
            if (!$savings_account) {
                throw new Exception('Customer doesn\'t have a savings account.', 400);
            }

            $product = $savings_account->savings_product;
            return response()->json(SavingsProductApiResource::make($product));
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Something went wrong. ' . $th->getMessage()
            ], 400);
        }
    }

    public function createAccount(Request $request)
    {
        $phone = $request->phone;
        $partner_code = $request->header('X-PARTNER-CODE');
        $product_code = $request->code;
        $customer = Customer::where('Telephone_Number', $phone)->first();
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $savings_product = SavingsProduct::where('code', $product_code)->first();
        DB::beginTransaction();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }

            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            // TODO MA.
            // Should the customer be allowed to have multiple savings accounts?
            // Customers don't belong to partners
            // But their savings account and savings product are owned by the partner
            $savings_account = $customer->savings_account;
            if ($savings_account) {
                throw new Exception('Customer already has a savings account. Only one account allowed at the moment.', 400);
            }

            if (!$savings_product) {
                throw new Exception('Savings product with that code does not exist.', 400);
            }

            if ($savings_product->partner_id != $partner->id) {
                throw new Exception('Savings product does not belong to the partner.', 400);
            }

            $savings_account = new SavingsAccount();
            $savings_account->partner_id = $partner->id;
            $savings_account->customer_id = $customer->id;
            $savings_account->savings_product_id = $savings_product->id;
            $savings_account->expected_amount = $savings_product->cost;
            $savings_account->save();

            SendSMSNotification::dispatch(new SavingsAccountCreatedNotification($phone, 'Your savings account has been created. You can now transact on your account. Thank you.'));

            DB::commit();
            return response()->json([
                'message' => "Savings account successfully created. Please wait for SMS confirmation.",
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }
}
