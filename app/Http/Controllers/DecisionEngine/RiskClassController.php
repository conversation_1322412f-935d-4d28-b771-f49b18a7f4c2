<?php

namespace App\Http\Controllers\DecisionEngine;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Partner;
use App\Models\DecisionEngine\RiskClass;
use App\Validators\UniquePartnerMinMax;
use App\Services\DecisionEngineService;
use Illuminate\Foundation\Validation\ValidatesRequests;

class RiskClassController extends Controller
{
    use ValidatesRequests;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return view("decision-engine.risk-classes.index");
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try {
            $risk_class_types = DecisionEngineService::RISK_CLASS_TYPES;
            return view("decision-engine.risk-classes.create", compact("risk_class_types"));
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $rules = [
            'class' => 'required',
            'type' => [
                'required',
                function ($attribute, $value, $fail) use ($request) {
                    // Check if a combination of class and type already exists
                    $exists = RiskClass::where('class', $request->input('class'))
                                       ->where('type', $value)
                                       ->exists();
                    if ($exists) {
                        $fail('The combination of risk class and risk class type must be unique.');
                    }
                },
            ],
        ];

        $customMessages = [
            'class.required' => 'Risk class is required.',
            'type.required' => 'Risk class type is required.',
        ];

        $this->validate($request, $rules, $customMessages);

        try {
            $data = $request->all();
            RiskClass::create($data);

            return redirect()
                ->route("decision-engine.risk-classes.index")
                ->withSuccess("Risk class created successfully");
        } catch (\Throwable $r) {
            return redirect()->back()->withErrors($r->getMessage())->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    { }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $data = RiskClass::find($id);
            $risk_class_types = DecisionEngineService::RISK_CLASS_TYPES;
            return view("decision-engine.risk-classes.edit", compact("data", "risk_class_types"));
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $rules = [
            'class' => 'required',
            'type' => [
                'required',
                function ($attribute, $value, $fail) use ($request, $id) {
                    // Check if a combination of class and type already exists, excluding the current record
                    $exists = RiskClass::where('class', $request->input('class'))
                                       ->where('type', $value)
                                       ->where('id', '!=', $id) // Exclude current record
                                       ->exists();
                    if ($exists) {
                        $fail('The combination of risk class and risk class type must be unique.');
                    }
                },
            ],
        ];

        $customMessages = [
            'class.required' => 'Risk class is required.',
            'type.required' => 'Risk class type is required.',
        ];

        $this->validate($request, $rules, $customMessages);

        try {
            $data = $request->all();

            // Update the existing RiskClass record with the provided data
            RiskClass::where('id', $id)->update($data);

            return redirect()
                ->route("decision-engine.risk-classes.index")
                ->withSuccess("Risk class updated successfully");
        } catch (\Throwable $r) {
            return redirect()->back()->withErrors($r->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            if (!RiskMultiplier::where('risk_class_id', $id)->exists()) {
              $data = RiskClass::findOrFail($id);
              $data->delete();
              return redirect()
                  ->back()
                  ->withSuccess("Risk class deleted successfully");
            }

            return redirect()
                ->back()
                ->withErrors("Risk class cannot be deleted");

        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }
}
