<?php

namespace App\Services\Integrations;

use App\Models\PartnerApiSetting;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class AtlasService
{
    protected PartnerApiSetting $api;

    protected ?string $accessToken;
    protected ?string $refreshToken;

    /**
     * @throws \Exception
     */
    public function __construct()
    {
        //$this->setApiConfiguration();
    }

    public function upload(string $file): string
    {
        $filename = basename($file);

        try {
            // Create a multipart request with the file properly attached
            $response = $this->client()
                ->attach('file', Storage::disk('local')->get($file), $filename, ['Content-Type' => 'text/csv'])
                ->post('lead/upload');

            // Check if the request was successful
            if ($response->failed()) {
                Log::debug('Upload API returned error: ' . $response->body());
                return '';
            }

            return data_get($response->json(), 'job_id');
        } catch (\Exception $exception) {
            Log::debug('Failed to upload file: '.$exception->getMessage());
            return '';
        }
    }

    protected function getFiles(?string $date = null): array
    {
        if (is_null($date)) {
            $date = now()->format('Ymd');
        }

        return Storage::disk('local')->files('partners/spiro/uploads/'.$date);
    }

    /**
     * @throws ConnectionException
     */
    public function refreshToken(): bool
    {
        $response = $this->getToken();

        if (! Arr::has($response, ['access_token', 'expires_in'])) {
            return false;
        }

        $this->updateToken($response);

        return true;
    }

    protected function updateToken($response): true
    {
        $this->api->update([
            'api_key' => Arr::get($response, 'access_token'),
            'refresh_token' => Arr::get($response, 'refresh_token'),
            'expires_at' => now()->addSeconds((int) Arr::get($response, 'expires_in', 180)),
        ]);

        $this->api->refresh();
        $this->accessToken = $this->api->api_key;
        $this->refreshToken = $this->api->refresh_token;

        return true;
    }

    /**
     * @throws ConnectionException
     */
    public function getToken()
    {
        return Http::baseUrl(config('services.partners.spiro_atlas.url'))
            ->get('oauth/token', [
                'client_id' => config('services.partners.spiro_atlas.client_key'),
                'client_secret' => config('services.partners.spiro_atlas.client_secret'),
                'refresh_token' => config('services.partners.spiro_atlas.refresh_token'),
            ])->json();
    }

    /**
     * @throws ConnectionException
     */
    protected function client(): \Illuminate\Http\Client\PendingRequest
    {
        $response = cache()
            ->flexible('atlas_access_token', [3000, 400], function () {
                return $this->getToken();
            });

        if (! Arr::has($response, ['access_token'])) {
            throw new ConnectionException('Failed to get access token');
        }

        return Http::retry(3, 1000)
            ->withHeaders([
                // Don't set Content-Type here as it will be set automatically for multipart requests
                'source-id' => config('services.partners.spiro_atlas.source_id'),
                'bearer-token' => Arr::get($response, 'access_token'),
            ])
            ->acceptJson()
            ->baseUrl(config('services.partners.spiro_atlas.url'));
    }

    /**
     * @throws ConnectionException
     */
    protected function setApiConfiguration(): self
    {
        $api = PartnerApiSetting::query()
            ->firstWhere(['api_name' => 'spiro_atlas']);

        if (empty($api)) {
            throw new \Exception('No configuration found');
        }

        $this->api = $api;

        if (now()->greaterThan($this->api->expires_at)) {
            $this->refreshToken();
        } else {
            $this->accessToken = $this->api->api_key;
            $this->refreshToken = $this->api->refresh_token;
        }


        // todo: Check if the token is expired

        if (empty($this->accessToken)) {
            throw new \Exception('No access token found');
        }

        return $this;
    }
}
