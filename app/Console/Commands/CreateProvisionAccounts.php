<?php

namespace App\Console\Commands;

use App\Models\LoanProduct;
use App\Models\Partner;
use Illuminate\Console\Command;
use App\Models\Accounts\Account;
use App\Services\SpiroApiService;
use App\Services\Account\AccountSeederService;
use Illuminate\Support\Str;

class CreateProvisionAccounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:create-provision-accounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create provision accounts';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        Partner::query()->each(function (Partner $partner) {
            $this->info('Creating or updating provision accounts for: ' . $partner->Institution_Name);

            $seeder = new AccountSeederService($partner->id);
            $accounts = [
                $seeder::LOAN_LOSS_PROVISION_SLUG => [
                    'name' => $seeder::LOAN_LOSS_PROVISION_NAME,
                    'identifier' => $seeder::LOAN_LOSS_PROVISION_IDENTIFIER,
                    'class' => null,
                    'parent-slug' => $seeder::LIABILITIES_SLUG,
                ],
                $seeder::RECOVERIES_FROM_WRITTEN_OFF_LOANS_SLUG => [
                    'name' => $seeder::RECOVERIES_FROM_WRITTEN_OFF_LOANS_NAME,
                    'identifier' => $seeder::RECOVERIES_FROM_WRITTEN_OFF_LOANS_IDENTIFIER,
                    'class' => null,
                    'parent-slug' => $seeder::INCOME_SLUG,
                ],
            ];
           foreach($accounts as $slug => $record) {
               $parent = Account::query()->where('slug', $record['parent-slug'])->where('partner_id', $partner->id)->first();

               if (empty($parent)) {
                   $this->error('Parent account ' . $record['parent-slug'] . ' not found');

                   return;
               }

               $account = new Account();
               $account->partner_id = $partner->id;
               $account->slug = $slug;
               $account->position = -1;
               $account->identifier = $record['identifier'];
               $account->name = $record['name'];
               $account->type_letter = $parent->type_letter;
               $account->is_fixed = true;
               $account->save();

               $parent->addFixedAccount($account, $record['identifier']);
           }
        });

        return 0;
    }
}
