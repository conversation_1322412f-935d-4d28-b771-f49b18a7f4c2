<?php

namespace App\Models;

use App\Models\Accounts\Account;
use App\Models\Scopes\PartnerScope;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class JournalEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        "partner_id",
        "customer_id",
        "account_id",
        "account_name",
        "cash_type", // Cash In, Cash Out
        "amount",
        "current_balance",
        "previous_balance",
        "txn_id",
        "transactable",
        "transactable_id",
        "accounting_type",
        "credit_amount",
        "debit_amount",
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new PartnerScope);
    }

    // Transactable
    public function journable(): \Illuminate\Database\Eloquent\Relations\MorphTo
    {
        return $this->morphTo('journable', 'transactable', 'transactable_id');
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function entryType(): Attribute
    {
        return Attribute::make(
            get: function () {
                return str($this->transactable)->afterLast('\\Loan')->title()->toString();
            }
        );
    }
}
