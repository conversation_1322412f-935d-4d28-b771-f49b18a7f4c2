<?php

namespace App\Console\Commands\DFCUTests;

use App\Actions\Loans\ApproveLoanApplication;
use App\Models\Transaction;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ApproveDownPayment extends Command
{
  protected $signature = 'dfcu:approve {id}';
  protected $description = 'Approve down payment for loan disbursement.';

  public function handle(ApproveLoanApplication $action): int
  {
    $transaction = Transaction::query()->where('TXN_ID', $this->argument('id'))->first();

    if (!$transaction) {
        $this->error('Transaction not found');

        return 1;
    }

    $action->execute($transaction->loanApplication);

    $this->info('Loan Application ID: '.$transaction->loanApplication->id.' approved successfully');

    return 0;
  }
}
