<?php

namespace App\Actions\Reports;

use App\Models\Customer;
use App\Models\Loan;
use App\Models\LoanLossProvision;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class GetLoanAgeingReportDetailsAction
{
    protected string $startDate = '';
    protected string $endDate = '';
    protected bool $excludeNotDue = false;
    protected int $perPage = 0;

    public function execute(): \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $provisions = LoanLossProvision::query()
            ->select('id', 'minimum_days', 'maximum_days')
            ->whereNotNull('approved_at')
            ->orderBy('minimum_days')
            ->get();

        $query = Loan::query()
            ->whereNotIn('Credit_Account_Status', [
                Loan::ACCOUNT_STATUS_WRITTEN_OFF,
                Loan::ACCOUNT_STATUS_FULLY_PAID_OFF
            ])->whereRelation('schedule', function ($query) {
                $query->where('principal_remaining', '>', 0);
            });

        if ($this->excludeNotDue) {
            $query->whereDoesntHave('schedule', function ($query) {
                return $query->where('payment_due_date', '>=', $this->endDate);
            });
        }

        $query->with('customer')
            ->withSum('schedule', 'principal_remaining')
            ->ageingCategories($this->endDate, $provisions);

        $query->orderBy(
            Customer::query()
                ->select('First_Name')
                ->whereColumn('customers.id', 'loans.customer_id')
                ->limit(1)
        );

        if ($this->perPage > 0) {
            return $query->paginate($this->perPage);
        }

        return $query->get();
    }

    public function paginate($perPage = 100): self
    {
        $this->perPage = $perPage;

        return $this;
    }

    public function filters(array $details): self
    {
        $this->endDate = Arr::get($details, 'endDate', now()->toDateString());
        $this->excludeNotDue = Arr::get($details, 'excludeNotDue', false);

        if (Carbon::parse($this->endDate)->isFuture() || empty($this->endDate)) {
            $this->endDate = now()->toDateString();
        }

        return $this;
    }
}
