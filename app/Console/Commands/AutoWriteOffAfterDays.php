<?php

namespace App\Console\Commands;

use App\Models\Loan;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AutoWriteOffAfterDays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:auto-write-off-after-days';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Starting auto write-off process...');
            Log::info('Starting auto write-off process...');

            $this->processWriteOffs();
            $this->info('Auto write-off process completed successfully.');
            Log::info('Auto write-off process completed successfully.');
        } catch (\Exception $e) {
            $this->error('Error during auto write-off: ' . $e->getMessage());
            Log::error('Error during auto write-off: ' . $e->getMessage());
        }
    }

    private function processWriteOffs()
    {
        $loans = Loan::with('product')->where('Credit_Account_Status', Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS)
            ->get();
        foreach ($loans as $loan) {
            $writeOffDays = $loan->product->Arrears_Auto_Write_Off_Days ?? 0;
            $today = Carbon::now()->startOfDay();
            $maturityDate = $loan->Maturity_Date;
            $daysOverdue = $today->diffInDays($maturityDate, true);
            if ($daysOverdue <= $writeOffDays) {
                Log::info("Loan ID {$loan->id} is over due but not eligible for auto write-off. Days overdue: {$daysOverdue}, Write-off days: {$writeOffDays}");
                $this->info("Loan ID {$loan->id} is over due but not eligible for auto write-off. Days overdue: {$daysOverdue}, Write-off days: {$writeOffDays}");
                continue; // Skip if not overdue enough
            }

            $details = [
                'write_off_date' => now()->toDateString(),
            ];
            app(\App\Actions\Loans\WriteOffLoanAction::class)->execute($loan, $details);
        }
    }
}
