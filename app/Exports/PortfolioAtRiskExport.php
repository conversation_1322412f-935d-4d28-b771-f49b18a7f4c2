<?php

namespace App\Exports;

use App\Actions\Reports\GetPortfolioAtRiskReportDetailsAction;
use App\Models\LoanLossProvision;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;

class PortfolioAtRiskExport implements FromView, WithTitle
{
    public function __construct(protected array $filters)
    {
    }

    public function view(): \Illuminate\Contracts\View\View
    {
        return view('excel.portfolio-at-risk', [
            'loans' => app(GetPortfolioAtRiskReportDetailsAction::class)
                ->filters($this->filters)
                ->execute(),
            'ageingDays' => $this->getAgeingDays(),
            'partnerName' => auth()->user()?->partner->Institution_Name,
            'filters' => $this->filters,
        ]);
    }

    public function title(): string
    {
        return 'Portfolio At Risk';
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection|mixed
     */
    public function getAgeingDays(): mixed
    {
        $provisions = LoanLossProvision::query()
            ->select('id', 'minimum_days', 'maximum_days')
            ->orderBy('minimum_days')
            ->get();

        if ($provisions->isEmpty()) {
            return collect([
                ['days' => '1 - 30'],
                ['days' => '31 - 60'],
                ['days' => '61 - 90'],
                ['days' => '91 - 180'],
                ['days' => '181 - Above'],
            ])->map(fn ($days) => (object) $days);
        }

        return $provisions;
    }
}
