<?php

namespace App\Services;

use App\Exceptions\MtnApiException;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\Transaction;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class MockMtnApiService extends MtnApiService
{

    /**
     * @throws MtnApiException
     */
    public function __construct(
        private readonly string $environment = 'test',
        private readonly array $config = []
    ) {
        parent::__construct($environment, $config);
    }

    /**
     * @throws MtnApiException
     */
    public function disburse(
        $phone_number,
        $amount,
        $txn_reference,
        $reason = "Withdraw"
    ): array {
        $this->validatePhoneNumber($phone_number);
        $this->validateAmount($amount);

        return [
            'status' => true,
            'status_code' => 200,
            'message' => $this->getStatusSuccessMessage(),
            'reference' => '91p38Xdd8eR4GQvep77lXO2jlFXvLZb',
            'data' => [],
        ];
    }

    public function disbursementStatus($txn_id): array
    {
        return $this->getMockTransactionStatusResponse(['referenceid' => $txn_id]);
    }

    /**
     * @throws MtnApiException
     */
    public function collect(
        string $phone_number,
        $amount,
        string $txn_reference,
        string $reason = "Deposit"
    ): array {
        $this->validatePhoneNumber($phone_number);
        $this->validateAmount($amount);

        $response = $this->getMockCollectionResponse([
            'amount' => ['amount' => $amount],
            'reference' => $txn_reference,
            'message' => $reason
        ]);

        $isSuccessful = Arr::get($response, 'status') === 'SUCCESSFUL';

        return [
            'status' => $isSuccessful,
            'status_code' => 200,
            'message' => $isSuccessful ? $this->getStatusSuccessMessage() : 'PENDING',
            'reference' => Arr::get($response, 'transactionid'),
            'data' => $response,
        ];
    }

    public function collectionStatus($collection_txn_id): array
    {
        return $this->getMockTransactionStatusResponse([
            'referenceid' => $collection_txn_id
        ]);
    }

    /**
     * @throws MtnApiException
     */
    public function getCustomerDetails(string $msisdn): array
    {
        $this->validatePhoneNumber($msisdn);
        return $this->getMockCustomerDetails([
            'resource' => "FRI:{$msisdn}/MSISDN"
        ]);
    }

    /**
     * @throws MtnApiException
     */
    public function optOut(Customer $customer): bool
    {
        $this->validatePhoneNumber($customer->Telephone_Number);

        return true;
    }

    public function customerRegistrationCompleted(Customer $customer): bool {
        return true;
    }

    public function bankDebitCompleted(Transaction $transaction): array {
        return [
            'externaltransactionid' => $transaction->Provider_TXN_ID,
            'status' => 'SUCCESSFUL'
        ];
    }

    public function customerValidation(
        string $externalRequestId,
        string $message,
        string $receiver,
        array $extension = []
    ): array {
        return [
            'approvalid' => '1253458'
        ];
    }

    /**
     * @throws MtnApiException
     */
    public function createLoanApplication(
        string $msisdn,
        float $amount,
        int $tenor
    ): array {
        $this->validatePhoneNumber($msisdn);
        $this->validateAmount($amount);
        $this->validateTenor($tenor);

        return [
            'loanaccount' => [
                'accountnumber' => '***********',
                'status' => 'PENDING',
                'duedate' => '2023-08-12+03:00',
                'tenor' => (int) $tenor,
                'loantype' => 'PERSONAL',
                'interest' => '1.5',
                'extension' => []
            ],
            'message' => 'Your Loan application is being processed. Ref ***********'
        ];
    }

    public function loanApplicationCompleted(Transaction $transaction): bool
    {
        return true;
    }

    /**
     * @throws MtnApiException
     */
    public function getLoanLimit(string $msisdn): array
    {
        $this->validatePhoneNumber($msisdn);
        return $this->getMockFinancialResourceInfo([
            'extension' => ['RequestType' => 'CheckLoanLimit']
        ]);
    }

    /**
     * @throws MtnApiException
     */
    public function getMiniStatement(string $msisdn): array
    {
        $this->validatePhoneNumber($msisdn);
        return $this->getMockFinancialResourceInfo([
            'extension' => ['RequestType' => 'ViewLast5Transactions']
        ]);
    }

    /**
     * @throws MtnApiException
     */
    public function refund(string $transactionId, float $amount): array
    {
        $this->validateAmount($amount);
        return [
            'financialtransactionid' => $transactionId,
            'refunderfee' => [
                'amount' => '0.00',
                'currency' => 'UGX'
            ]
        ];
    }

    public function customerValidationCompleted(
        string $externalRequestId,
        string $receiver,
        string $validationResult,
        string $approvalId,
        array $extension = []
    ): array {
        return [];
    }

    /**
     * Get mock transaction status response
     */
    private function getMockTransactionStatusResponse(array $data): array
    {
        $referenceId = $data['referenceid'] ?? $data['financialtransactionid'] ?? '';

        return [
            'status' => true,
            'status_code' => 200,
            'message' => 'SUCCEEDED',
            'reference' => $referenceId,
        ];
    }

    /**
     * Get mock collection response
     */
    private function getMockCollectionResponse(array $data): array
    {
        $amount = $data['amount']['amount'] ?? 0;

        // Simulate random success/failure
        if (rand(0, 10) === 0) { // 10% chance of failure
            throw new MtnApiException('Insufficient funds for transaction');
        }

        return [
            'transactionid' => (string) rand(10000000, 99999999),
            'status' => 'SUCCESSFUL',
            'amount' => [
                'amount' => (string) $amount,
                'currency' => 'UGX'
            ]
        ];
    }

    /**
     * Get mock customer details based on registration status
     */
    private function getMockCustomerDetails(array $data): array
    {
        $resource = $data['resource'] ?? 'FRI:************/MSISDN';
        $msisdn = str_replace(['FRI:', '/MSISDN'], '', $resource);

        // Randomly return different customer states for testing
        $status = rand(0, 10);

        return match ($status) {
            0 => [
                'customerid' => $msisdn,
                'customerName' => 'TEST MK O',
                'status' => 'REGISTERED',
                'savingsaccounts' => [
                    'savingsaccount' => [
                        'accountnumber' => '17409487042748307966936535004917',
                        'status' => 'ACTIVE',
                        'balance' => [
                            'amount' => '0.00',
                            'currency' => 'UGX'
                        ],
                        'savingsaccounttype' => 'SAVINGS'
                    ]
                ],
                'loanaccounts' => [
                    'loanaccount' => [
                        'accountnumber' => '23703387890474491380402928105815',
                        'status' => 'APPROVED',
                        'due' => [
                            'amount' => '0',
                            'currency' => 'UGX'
                        ],
                        'duedate' => '2021-09-03',
                        'loantype' => 'PERSONAL',
                        'interest' => '0.0',
                        'extension' => [
                            'BankWallet' => 'MOS-PERSONAL'
                        ]
                    ]
                ]
            ],
            1 => [
                'status' => 'UNREGISTERED',
                'savingsaccounts' => [],
                'loanaccounts' => []
            ],
            default => [
                'status' => 'REGISTERED',
                'customerName' => 'TEST MK O',
                'savingsaccounts' => [
                    'savingsaccount' => [
                        'accountnumber' => '17409487042748307966936535004917',
                        'status' => 'ACTIVE',
                        'balance' => [
                            'amount' => '0.00',
                            'currency' => 'UGX'
                        ],
                        'savingsaccounttype' => 'SAVINGS'
                    ]
                ],
                'loanaccounts' => [
                    'loanaccount' => [
                        'accountnumber' => '23703387890474491380402928105815',
                        'status' => 'APPROVED',
                        'due' => [
                            'amount' => '0',
                            'currency' => 'UGX'
                        ],
                        'duedate' => '2021-09-03',
                        'loantype' => 'PERSONAL',
                        'interest' => '0.0',
                        'extension' => [
                            'BankWallet' => 'MOS-PERSONAL'
                        ]
                    ]
                ]
            ]
        };
    }

    /**
     * Get mock financial resource information based on request type
     */
    private function getMockFinancialResourceInfo(array $data): array
    {
        $extension = $data['extension'] ?? [];
        $requestType = $extension['RequestType'] ?? '';

        return match ($requestType) {
            'CheckLoanLimit' => [
                'message' => 'Your limit is UGX 10000.00',
                'extension' => [
                    'customerName' => 'TEST MK O',
                    'CardNo' => '***********',
                    'amount' => '10000.00',
                    'currency' => 'UGX',
                    'Status' => '101'
                ]
            ],
            'ViewLast5Transactions' => [
                'message' => 'No Transactions',
                'extension' => []
            ],
            default => [
                'message' => 'Yello, your request to opt out of Equi Loans has been submitted',
                'extension' => [
                    'status' => '101'
                ]
            ]
        };
    }

    /**
     * Validate a phone number format
     *
     * @param string $msisdn Phone number to validate
     * @throws MtnApiException
     */
    private function validatePhoneNumber(string $msisdn): void
    {
        if (!preg_match('/^256[0-9]{9}$/', $msisdn)) {
            throw new MtnApiException('Invalid phone number format. Must be in format: 256XXXXXXXXX');
        }
    }

    /**
     * Validate amount is positive
     *
     * @param float $amount Amount to validate
     * @throws MtnApiException
     */
    private function validateAmount(float $amount): void
    {
        if ($amount <= 0) {
            throw new MtnApiException('Amount must be greater than 0');
        }
    }

    /**
     * Validate tenor is within allowed range
     *
     * @param int $tenor Loan tenor in days
     * @throws MtnApiException
     */
    private function validateTenor(int $tenor): void
    {
        if ($tenor < 1 || $tenor > 3) {
            throw new MtnApiException('Tenor must be between 1 and 3 days');
        }
    }
}
