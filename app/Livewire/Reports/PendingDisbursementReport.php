<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetPendingDisbursementReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Exports\PendingDisbursementExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class PendingDisbursementReport extends Component
{
    use ExportsData, WithPagination;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.pending-disbursement-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.pending-disbursements', [
                'records' => app(GetPendingDisbursementReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters()
            ])
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new PendingDisbursementExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetPendingDisbursementReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }
}
