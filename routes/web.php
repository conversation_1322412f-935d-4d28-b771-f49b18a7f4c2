<?php

use App\Http\Controllers\AgentController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\layouts\Blank;
use App\Http\Controllers\layouts\Fluid;
use App\Http\Controllers\SmsController;
use App\Http\Controllers\TaxController;
use App\Http\Controllers\HitsController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\icons\Boxicons;
use App\Http\Controllers\LoanController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\cards\CardBasic;
use App\Http\Controllers\pages\MiscError;
use App\Http\Middleware\EnforceTwoFactor;
use App\Http\Controllers\layouts\Container;
use App\Http\Controllers\PartnerController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SwitchesController;
use App\Http\Controllers\layouts\WithoutMenu;
use App\Http\Controllers\AuditTrailController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\DefferedIncomeReport;
use App\Http\Controllers\FeesReportController;
use App\Http\Controllers\FLoatTopUpController;
use App\Http\Controllers\layouts\WithoutNavbar;
use App\Http\Controllers\LoanProductController;
use App\Http\Controllers\LoanReportsController;
use App\Http\Controllers\SmsCampaignController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\user_interface\Alerts;
use App\Http\Controllers\user_interface\Badges;
use App\Http\Controllers\user_interface\Footer;
use App\Http\Controllers\user_interface\Modals;
use App\Http\Controllers\user_interface\Navbar;
use App\Http\Controllers\user_interface\Toasts;
use App\Http\Controllers\BusinessRuleController;
use App\Http\Controllers\SmsTemplatesController;
use App\Http\Controllers\user_interface\Buttons;
use App\Http\Controllers\AssetLocationController;
use App\Http\Controllers\extended_ui\TextDivider;
use App\Http\Controllers\user_interface\Carousel;
use App\Http\Controllers\user_interface\Collapse;
use App\Http\Controllers\user_interface\Progress;
use App\Http\Controllers\user_interface\Spinners;
use App\Http\Controllers\form_elements\BasicInput;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\SavingsAccountController;
use App\Http\Controllers\SavingsDepositController;
use App\Http\Controllers\SavingsProductController;
use App\Http\Controllers\SavingsReportsController;
use App\Http\Controllers\user_interface\Accordion;
use App\Http\Controllers\user_interface\Dropdowns;
use App\Http\Controllers\user_interface\Offcanvas;
use App\Http\Controllers\user_interface\TabsPills;
use App\Http\Controllers\AtAGlanceReportController;
use App\Http\Controllers\BorrowersReportController;
use App\Http\Controllers\ChartOfAccountsController;
use App\Http\Controllers\form_elements\InputGroups;
use App\Http\Controllers\form_layouts\VerticalForm;
use App\Http\Controllers\LoanApplicationController;
use App\Http\Controllers\LoanProductFeesController;
use App\Http\Controllers\LoanProductTermController;
use App\Http\Controllers\Loans\LoanAssetController;
use App\Http\Controllers\SavingsProviderController;
use App\Http\Controllers\user_interface\ListGroups;
use App\Http\Controllers\user_interface\Typography;
use App\Http\Controllers\FinancialReportsController;
use App\Http\Controllers\LoanProductAddonController;
use App\Http\Controllers\LoanProductReportConroller;
use App\Http\Controllers\pages\MiscUnderMaintenance;
use App\Http\Controllers\form_layouts\HorizontalForm;

use App\Http\Controllers\tables\Basic as TablesBasic;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\ExclusionParameterController;
use App\Http\Controllers\extended_ui\PerfectScrollbar;
use App\Http\Controllers\pages\AccountSettingsAccount;
use App\Http\Controllers\SavingsApplicationController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\CashSaleController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\dashboard\DashboardController;
use App\Http\Controllers\LabelController;
use App\Http\Controllers\LoanProductPenaltiesController;
use App\Http\Controllers\user_interface\TooltipsPopovers;
use App\Http\Controllers\pages\AccountSettingsConnections;
use App\Http\Controllers\pages\AccountSettingsNotifications;
use App\Http\Controllers\Reports\LoanApplicationReportController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\TicketReportController;
use App\Http\Controllers\user_interface\PaginationBreadcrumbs;
use Illuminate\Support\Facades\Log;

Auth::routes();

Route::get('/home', [HomeController::class, 'index'])->name('home');

Route::post('login', [LoginController::class, 'login']);
Route::post('logout', [LoginController::class, 'logout'])->name('logout');
Route::get('refresh-csrf-token', [LoginController::class, 'refreshCsrfToken'])->name('refresh-csrf-token');

Route::get('forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('forgot-password.index');
Route::post('forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('forgot-password.send-email');
Route::get('password/reset', [ResetPasswordController::class, 'showResetForm'])->name('password.showResetForm');
//Route::post('password/reset', [ResetPasswordController::class, 'reset'])->name('password.reset');
Route::post('password/email', [ResetPasswordController::class, 'sendResetLinkEmail'])->name('password.email');

Route::middleware('auth')->group(function () {
	Route::post('/users/verify-2fa', [UserController::class, 'verify2faCode'])->name('users.verify-2fa');
	Route::get('/verify-2fa', [UserController::class, 'show2faLoginScreen'])->name('verify-2fa');
});
// Ticketing APIs
Route::middleware('auth')->group(function () {
	// Regular ticket routes
	Route::get('/ticket-reports', [TicketReportController::class, 'index'])->name('tickets.reports');
	Route::get('/tickets/reports/export/pdf', [TicketReportController::class, 'exportPdf'])->name('tickets.reports.export.pdf');
	Route::get('/tickets/reports/export/excel', [TicketReportController::class, 'exportExcel'])->name('tickets.reports.export.excel');
	Route::resource('tickets', TicketController::class)->except(['edit', 'update', 'destroy']);
	Route::middleware('permission:view ticket-dashboard')->get('ticket-dashboard', [TicketController::class, 'dashboard'])
		->name('tickets.dashboard');
	Route::post('tickets/{ticket}/comment', [TicketController::class, 'addComment'])
		->name('tickets.comment');
	Route::resource('agents', AgentController::class)->middleware('permission:delete agents');

	// Admin ticket actions
	Route::middleware('permission:update tickets')->put('tickets/{ticket}/status', [TicketController::class, 'updateStatus'])
		->name('tickets.status');
	Route::middleware('permission:update tickets')->post('tickets/{ticket}/assign', [TicketController::class, 'assign'])
		->name('tickets.assign');
	Route::resource('categories', CategoryController::class)->except(['show', 'create']);
	Route::resource('labels', LabelController::class)->except(['show', 'create']);
});
Route::get('/test-slack-log', function () {
	Log::error('🚨 This is a test error to Slack!');
	return 'Slack log sent!';
});
Route::middleware(['auth', EnforceTwoFactor::class])->group(function () {
	// Dashboard
	Route::get('/', [DashboardController::class, 'index'])->name('dashboard.index');
	Route::get('/asset-dashboard', [DashboardController::class, 'savingsDashboard'])->name('dashboard.savings-dashboard');
	Route::get('/notifications/mark-as-read/{notification}', [DashboardController::class, 'markAsRead'])->name('notifications.markAsRead');
	// Users
	Route::middleware('permission:view users')->get('/users', [UserController::class, 'index'])->name('users.index');
	Route::middleware('permission:create users')->get('/users/create', [UserController::class, 'create'])->name('users.create');
	Route::middleware('permission:create users')->post('/users', [UserController::class, 'store'])->name('users.store');
	Route::middleware('permission:update users')->get('/users/{user}', [UserController::class, 'show'])->name('users.show');
	Route::middleware('permission:update users')->get('/users/{user}/edit', [UserController::class, 'edit'])->name('users.edit');
	Route::middleware('permission:update users')->put('/users/{user}', [UserController::class, 'update'])->name('users.update');
	Route::middleware('permission:delete users')->delete('/users/{user}', [UserController::class, 'delete'])->name('users.delete');
	Route::middleware('permission:update users')->put('/users/{user}/update-password', [UserController::class, 'updatePassword'])->name('users.update-password');
	Route::middleware('permission:update users')->put('/users/{user}/enable-2fa', [UserController::class, 'enable2fa'])->name('users.enable-2fa');
	Route::middleware('permission:update users')->put('/users/{user}/confirm-2fa-code', [UserController::class, 'confirm2faCode'])->name('users.confirm-2fa-code');
	Route::middleware('permission:update users')->put('users/deactivate/{user}', [UserController::class, 'deactivate'])->name('user.deactivate');
	Route::middleware('permission:update users')->put('users/activate/{user}', [UserController::class, 'activate'])->name('user.activate');

	// Taxes
	Route::middleware('permission:view taxes')->get('taxes', [TaxController::class, 'index'])->name('taxes.index');
	Route::middleware('permission:create taxes')->get('taxes/create', [TaxController::class, 'create'])->name('taxes.create');
	Route::middleware('permission:create taxes')->post('taxes', [TaxController::class, 'store'])->name('taxes.store');
	Route::middleware('permission:view taxes')->get('taxes/{tax}', [TaxController::class, 'edit'])->name('taxes.edit');
	Route::middleware('permission:update taxes')->put('taxes/{tax}', [TaxController::class, 'update'])->name('taxes.update');
	Route::middleware('permission:delete taxes')->delete('taxes/{tax}', [TaxController::class, 'delete'])->name('taxes.destroy');

	// Partners
	Route::middleware('permission:view partners')->get('partners', [PartnerController::class, 'index'])->name('partners.index');
	Route::middleware('permission:create partners')->get('partners/create', [PartnerController::class, 'create'])->name('partner.create');
	Route::middleware('permission:create partners')->post('partners', [PartnerController::class, 'store'])->name('partner.store');
	Route::middleware('permission:update partners')->get('partners/{partner}', [PartnerController::class, 'edit'])->name('partner.edit');
	Route::middleware('permission:update partners')->put('partners/{partner}', [PartnerController::class, 'update'])->name('partner.update');
	Route::middleware('permission:delete partners')->delete('partners/{partner}', [PartnerController::class, 'delete'])->name('partner.destroy');
	Route::middleware('permission:update partners')->get('partners/{partner}/show', [PartnerController::class, 'show'])->name('partner.show');
	Route::middleware('permission:update partners')->put('partners/{partner}/ova', [PartnerController::class, 'saveOvaConfigs'])->name('partner.ova.create');
	Route::middleware('permission:update partners')->put('partners/{partner}/api', [PartnerController::class, 'saveApiConfigs'])->name('partner.api.create');
	Route::middleware('permission:delete partners')->put('partners/{partner}/api/delete', [PartnerController::class, 'deleteApiConfigs'])->name('partner.api.delete');

	// Customers
	Route::middleware('permission:view customers')->get('customers', [CustomerController::class, 'index'])->name('customers.index');
	Route::middleware('permission:create customers')->get('customers/create', [CustomerController::class, 'create'])->name('customer.create');
	Route::middleware('permission:create customers')->post('customers', [CustomerController::class, 'store'])->name('customer.store');
	Route::middleware('permission:view customers')->get('customers/{customer}/edit', [CustomerController::class, 'bulkUploadUI'])->name('customer.edit');
	Route::middleware('permission:view customers')->get('customers/{customer}', [CustomerController::class, 'show'])->name('customer.show');
	Route::middleware('permission:update customers')->put('customers/{customer}', [CustomerController::class, 'update'])->name('customer.update');
	Route::middleware('permission:delete customers')->delete('customers/{customer}', [CustomerController::class, 'delete'])->name('customer.destroy');
	Route::middleware('permission:view customers')->get('/customers/upload/ui', [CustomerController::class, 'bulkUploadUI'])->name('customer.upload.ui');
	Route::middleware('permission:create customers')->post('/customer/upload', [CustomerController::class, 'saveBulkUploadUI'])->name('customer.upload.submit');
	Route::middleware('permission:view customers')->get('/loan-customers/search', [CustomerController::class, 'findLoanCustomers'])->name('loan-customers.search');
	Route::middleware('permission:view customers')->get('/reports/customers/blacklisted', [CustomerController::class, 'blackListedReport'])->name('reports.customers.black-listed-report');
	Route::middleware('permission:create customers')->post('/customers/blacklist', [CustomerController::class, 'blacklistCustomer'])->name('customer.blacklist');
	Route::middleware('permission:create customers')->post('/customers/unblacklist', [CustomerController::class, 'unblacklistCustomer'])->name('customer.unblacklist');

	// Audit Trail
	Route::middleware('permission:view audit-trail')->get('audit-trail', [AuditTrailController::class, 'index'])->name('audit-trail.index');

	// Savings Product
	Route::middleware('permission:view savings-product')->get('savings-product', [SavingsProductController::class, 'index'])->name('savings-product.index');
	Route::middleware('permission:create savings-product')->post('savings-product', [SavingsProductController::class, 'store'])->name('savings-product.store');
	Route::middleware('permission:create savings-product')->get('savings-product/create', [SavingsProductController::class, 'create'])->name('savings-product.create');
	Route::middleware('permission:update savings-product')->put('savings-product/{savings_product}', [SavingsProductController::class, 'update'])->name('savings-product.update');
	Route::middleware('permission:delete savings-product')->delete('savings-product/{savings_product}', [SavingsProductController::class, 'delete'])->name('savings-product.destroy');
	Route::middleware('permission:view savings-product')->get('savings-product/{savings_product}/edit', [SavingsProductController::class, 'edit'])->name('savings-product.edit');

	// Loan Products
	Route::middleware('permission:view loan-products')->get('loan-products', [LoanProductController::class, 'index'])->name('loan-products.index');
	Route::middleware('permission:create loan-products')->get('loan-products/create', [LoanProductController::class, 'create'])->name('loan-products.create');
	Route::middleware('permission:create loan-products')->post('loan-products', [LoanProductController::class, 'store'])->name('loan-products.store');
	Route::middleware('permission:view loan-products')->get('loan-products/{loanProduct}/edit', [LoanProductController::class, 'edit'])->name('loan-products.edit');
	Route::middleware('permission:update loan-products')->put('loan-products/{loanProduct}', [LoanProductController::class, 'update'])->name('loan-products.update');
	Route::middleware('permission:view loan-products')->get('loan-products/{loanProduct}', [LoanProductController::class, 'show'])->name('loan-products.show');
	Route::middleware('permission:delete loan-products')->delete('loan-products/{loanProduct}', [LoanProductController::class, 'delete'])->name('loan-products.destroy');

	// Savings Accounts
	Route::middleware('permission:view savings-accounts')->get('savings-accounts', [SavingsAccountController::class, 'index'])->name('savings-accounts.index');
	Route::middleware('permission:create savings-accounts')->get('savings-accounts/create', [SavingsAccountController::class, 'create'])->name('savings-accounts.create');
	Route::middleware('permission:update savings-accounts')->put('savings-accounts/{savings}', [SavingsAccountController::class, 'update'])->name('savings-accounts.update');
	Route::middleware('permission:delete savings-accounts')->delete('savings-accounts/{savings}', [SavingsAccountController::class, 'delete'])->name('savings-accounts.destroy');
	Route::middleware('permission:view savings-accounts')->get('savings-accounts/{savings_}/edit', [SavingsAccountController::class, 'edit'])->name('savings-accounts.edit');
	Route::middleware('permission:view savings-accounts')->get('savings-deposits', [SavingsDepositController::class, 'index'])->name('savings-deposits.index');
	Route::middleware('permission:view savings-accounts')->get('savings-withdraws', [\App\Http\Controllers\SavingsWithdrawController::class, 'index'])->name('savings-withdraws.index');
	Route::middleware('permission:view savings-accounts')->get('savings-preferences', [SavingsAccountController::class, 'savingsPreferences']);
	Route::middleware('permission:view savings-accounts')->get('savings-applications', [SavingsAccountController::class, 'savingsApplications']);
	Route::middleware('permission:view savings-accounts')->get('savings-applications/{savingsApplication}', [SavingsApplicationController::class, 'show'])
		->name('savingsApplications.show');
	Route::middleware('permission:view savings-accounts')->patch('applications/{id}/update-status', [SavingsAccountController::class, 'updateStatus'])->name('applications.updateStatus');
	Route::middleware('permission:view savings-accounts')->patch('applications/{savingsApplication}/update-document-status', [SavingsAccountController::class, 'updateDocumentStatus'])->name('applications.updateDocumentStatus');
	Route::middleware('permission:view savings-accounts')->get('savings-providers', [SavingsProviderController::class, 'index'])->name('savings-providers.index');	// Loan Product Fees
	Route::middleware('permission:view savings-accounts')->get('savings-providers/{id}/edit', [SavingsProviderController::class, 'edit'])->name('savings-providers.edit');
	Route::middleware('permission:view savings-accounts')->patch('savings-providers/{id}', [SavingsProviderController::class, 'update'])->name('savings-providers.update');
	Route::middleware('permission:view savings-accounts')->get('savings-providers/create', [SavingsProviderController::class, 'create'])->name('savings-providers.create');
	Route::middleware('permission:view savings-accounts')->post('savings-providers', [SavingsProviderController::class, 'store'])->name('savings-providers.store');

	Route::middleware('permission:view loan-products')->get('loan-product-fees', [LoanProductFeesController::class, 'index'])->name('loan-product-fees.index');
	Route::middleware('permission:create loan-products')->get('loan-product-fees/create', [LoanProductFeesController::class, 'create'])->name('loan-product-fee.create');
	Route::middleware('permission:create loan-products')->post('loan-product-fees', [LoanProductFeesController::class, 'store'])->name('loan-product-fee.store');
	Route::middleware('permission:view loan-products')->get('loan-product-fees/{fee}', [LoanProductFeesController::class, 'edit'])->name('loan-product-fee.edit');
	Route::middleware('permission:update loan-products')->put('loan-product-fees/{fee}', [LoanProductFeesController::class, 'update'])->name('loan-product-fee.update');
	Route::middleware('permission:delete loan-products')->delete('loan-product-fees/{fee}', [LoanProductFeesController::class, 'delete'])->name('loan-product-fee.destroy');

	// Decision Engine
	Route::middleware('permission:view exclusion-parameters')->get('decision-engine/exclusion-parameters', [ExclusionParameterController::class, 'index'])->name('exclusion-parameters.index');
	Route::middleware('permission:create exclusion-parameters')->get('decision-engine/exclusion-parameters/create', [ExclusionParameterController::class, 'create'])->name('exclusion-parameter.create');
	Route::middleware('permission:create exclusion-parameters')->post('decision-engine/exclusion-parameters', [ExclusionParameterController::class, 'store'])->name('exclusion-parameter.store');
	Route::middleware('permission:view exclusion-parameters')->get('decision-engine/exclusion-parameters/{exclusionParameter}', [ExclusionParameterController::class, 'edit'])->name('exclusion-parameter.edit');
	Route::middleware('permission:update exclusion-parameters')->put('decision-engine/exclusion-parameters/{exclusionParameter}', [ExclusionParameterController::class, 'update'])->name('exclusion-parameter.update');
	Route::middleware('permission:delete exclusion-parameters')->delete('decision-engine/exclusion-parameters/{exclusionParameter}', [ExclusionParameterController::class, 'delete'])->name('exclusion-parameter.destroy');

	// Business Rules
	Route::middleware('permission:view business-rules')->get('business-rules', [BusinessRuleController::class, 'index'])->name('business-rules.index');
	Route::middleware('permission:create business-rules')->get('business-rules/create', [BusinessRuleController::class, 'create'])->name('business-rule.create');
	Route::middleware('permission:create business-rules')->post('business-rules', [BusinessRuleController::class, 'store'])->name('business-rule.store');
	Route::middleware('permission:view business-rules')->get('business-rules/{businessRule}', [BusinessRuleController::class, 'edit'])->name('business-rule.edit');
	Route::middleware('permission:update business-rules')->put('business-rules/{businessRule}', [BusinessRuleController::class, 'update'])->name('business-rule.update');
	Route::middleware('permission:delete business-rules')->delete('business-rules/{businessRule}', [BusinessRuleController::class, 'delete'])->name('business-rule.destroy');

	// Transactions
	Route::middleware('permission:view transactions')->get('transactions', [TransactionController::class, 'index'])->name('transactions.index');

	// Chart of Accounts

	Route::middleware('permission:view chart-of-accounts')->get('/chart-of-accounts', [ChartOfAccountsController::class, 'index'])->name('chart-of-accounts.index');
	Route::middleware('permission:view chart-of-accounts')->get('/chart-of-accounts/{account}', [ChartOfAccountsController::class, 'show'])->name('chart-of-accounts.show');
	Route::middleware('permission:view chart-of-accounts')->put('/chart-of-accounts/{accountId}', [ChartOfAccountsController::class, 'update'])->name('chart-of-accounts.update');
	Route::middleware('permission:create chart-of-accounts')->post('/chart-of-accounts', [ChartOfAccountsController::class, 'store'])->name('chart-of-accounts.store');


	// Loan Product Penalties
	Route::middleware('permission:view loan-products')->get('loan-product-penalty', [LoanProductPenaltiesController::class, 'index'])->name('loan-product-penalty.index');
	Route::middleware('permission:create loan-products')->get('loan-product-penalty/create', [LoanProductPenaltiesController::class, 'create'])->name('loan-product-penalty.create');
	Route::middleware('permission:create loan-products')->post('loan-product-penalty', [LoanProductPenaltiesController::class, 'store'])->name('loan-product-penalty.store');
	Route::middleware('permission:view loan-products')->get('loan-product-penalty/{penalty}', [LoanProductPenaltiesController::class, 'edit'])->name('loan-product-penalty.edit');
	Route::middleware('permission:update loan-products')->put('loan-product-penalty/{penalty}', [LoanProductPenaltiesController::class, 'update'])->name('loan-product-penalty.update');
	Route::middleware('permission:delete loan-products')->delete('loan-product-penalty/{penalty}', [LoanProductPenaltiesController::class, 'delete'])->name('loan-product-penalty.destroy');

	// Loan Accounts
	Route::middleware('permission:view loan-accounts')->get('/loan-accounts', [LoanController::class, 'index'])->name('loan-accounts.index');
	Route::middleware('permission:view loan-accounts')->put('/loan-accounts/{loan}/write-off', [LoanController::class, 'writeOff'])->name('loan-accounts.writeOff');
	Route::middleware('permission:view loan-accounts')->get('/loan-accounts/{loan}', [LoanController::class, 'show'])->name('loan-accounts.show');
	Route::middleware('permission:view loan-accounts')->get('/loan-accounts/{loan}/ledger', [LoanController::class, 'ledger'])->name('loan-accounts.ledger');
	Route::middleware('permission:view loan-accounts')->get('/loan-accounts/{loan}/payment-velocity', [LoanController::class, 'paymentVelocity'])->name('loan-accounts.paymentVelocity');
	Route::middleware('permission:view loan-accounts')->get('/loan-assets', [LoanAssetController::class, 'index'])->name('loan-assets.index');
	Route::middleware('permission:update loan-accounts')->put('/loan-accounts/{loan}', [LoanController::class, 'update'])->name('loan-accounts.update');
	Route::middleware('permission:update loan-accounts')
		->put('/loan-accounts/{loan}/enable-restructure', \App\Http\Controllers\AllowLoanScheduleRestructure::class)
		->name('loan-accounts.enableRestructure');

	// Loan Applications
	Route::middleware('permission:view loan-applications')->get('loan-applications', [LoanApplicationController::class, 'index'])->name('loan-applications.index');
	Route::get('loan-applications/create', [LoanApplicationController::class, 'create'])->name('loan-applications.create');
	Route::middleware('permission:create loan-applications')->post('loan-applications', [LoanApplicationController::class, 'store'])->name('loan-applications.store');
	Route::middleware('permission:edit loan-applications')->get('loan-application/{application}/edit', [LoanApplicationController::class, 'edit'])->name('loan-applications.edit');
	Route::middleware('permission:update loan-applications')->put('loan-application/{application}', [LoanApplicationController::class, 'update'])->name('loan-applications.update');
	Route::middleware('permission:view loan-applications')->get('loan-application/{application}', [LoanApplicationController::class, 'show'])->name('loan-applications.show');
	Route::middleware('permission:view loan-applications')->post('loan-approval/{loanApplication}', [LoanController::class, 'store'])->name('loan-application.approve');
	Route::middleware('permission:view loan-applications')->post('loan-applications/{transaction}/disburse', [LoanApplicationController::class, 'disburseAssetLoan'])->name('loan-application.disburse-loan');
	Route::middleware('permission:view loan-applications')->post('reject-loan-application/{loanApplication}', [LoanApplicationController::class, 'reject'])->name('loan-application.reject');
	Route::middleware('permission:view loan-applications')->get('/loan-application/{loan}/download', [LoanApplicationController::class, 'download'])->name('loan-applications.download');
	Route::middleware('permission:view loan-applications')->get('loan-disbursement/{loan}', [LoanController::class, 'disburse'])->name('loan-application.disburse');
	Route::middleware('permission:view loan-applications')->get('loan-repayment/{loan}', [LoanController::class, 'makePayment'])->name('loan-application.pay');
	Route::middleware('permission:view loan-applications')->post('loan-applications/{customer}', [LoanApplicationController::class, 'generateLoanSummary'])->name('loan-applications.summary');
	Route::middleware('permission:create loan-applications')->get('cancel-loan-applications', [LoanApplicationController::class, 'cancel'])->name('loan-application.cancel');

	// Loans
	Route::middleware('permission:view no-repayments')->get("/no-repayments", [LoanController::class, 'noRepayments'])->name('no-repayments');
	Route::middleware('permission:view past-maturity-date')->get("/past-maturity-date", [LoanController::class, 'pastMaturityDate'])->name('past-maturity-date');
	Route::middleware('permission:view principal-outstanding')->get("/principal-outstanding", [LoanController::class, 'principalOutstanding'])->name('principal-outstanding');
	Route::middleware('permission:view 1-month-late-loans')->get("/1-month-late-loans", [LoanController::class, 'oneMonthLateLoans'])->name('one-month-late-loans');
	Route::middleware('permission:view 3-months-late-loans')->get("/3-months-late-loans", [LoanController::class, 'threeMonthsLateLoans'])->name('three-months-late-loans');
	Route::middleware('permission:view downpayments')->get('downpayments', [\App\Http\Controllers\DownpaidLoanApplicationController::class, 'index'])->name('downpayments.index');
	Route::middleware('permission:view downpayments')->get('downpayments/{loanApplication}', [\App\Http\Controllers\DownpaidLoanApplicationController::class, 'show'])->name('downpayments.show');

	// Loan Repayment
	Route::middleware('permission:view loan-applications')->post('loan-repayment/{loan}', [LoanController::class, 'repayment'])->name('loan.repayment');
	//Route::middleware('permission:create loan-applications')->get('loan-repayment/create/{loan}', [LoanApplicationController::class, 'create'])->name('loan-repayment.create');
	//Route::middleware('permission:create loan-applications')->post('loan-repayment/{loan}', [LoanApplicationController::class, 'store'])->name('loan-repayment.store');

	// Float Management
	Route::middleware('permission:view float-management')->get('/float-management', [FLoatTopUpController::class, 'index'])->name('float-management.index');
	Route::middleware('permission:create float-management')->post('/float-management', [FLoatTopUpController::class, 'store'])->name('float-management.store');
	Route::middleware('permission:update float-management')->put('/float-management/approve/{topup}', [FLoatTopUpController::class, 'approve'])->name('float-management.approve');

	// Loan Product Terms
	Route::middleware('permission:create loan-product-terms')->post('/loan-product-terms', [LoanProductTermController::class, 'store'])->name('loan-product-term.store');
	Route::middleware('permission:update loan-product-terms')->put('/loan-product-terms/{loanProductTerm}', [LoanProductTermController::class, 'update'])->name('loan-product-term.update');
	Route::middleware('permission:delete loan-product-terms')->delete('/loan-product-terms/{loanProductTerm}', [LoanProductTermController::class, 'destroy'])->name('loan-product-term.destroy');

	// SMS Logs
	Route::middleware('permission:view sms-logs')->get('sms-logs', [SmsController::class, 'logs'])->name('sms.logs');
	Route::middleware('permission:view sms-float-topups')->get('sms-float-topups', [SmsController::class, 'topups'])->name('sms.topups');
	Route::middleware('permission:view sms-float-topups')->get('sms-float-topups/create', [SmsController::class, 'topupCreate'])->name('sms.topup-create');
	Route::middleware('permission:create sms-float-topups')->post('sms-float-topups/store', [SmsController::class, 'topupStore'])->name('sms.topup-store');
	Route::middleware('permission:view sms-float-topups')->get('download/{file}', [SmsController::class, 'download'])->name('download');
	Route::middleware('permission:update sms-float-topups')->post('sms-float-topups/approve/{id}', [SmsController::class, 'approveTopup'])->name('sms.approve-topup');
	Route::middleware('permission:update sms-float-topups')->post('sms-float-topups/reject/{id}', [SmsController::class, 'rejectTopup'])->name('sms.reject-topup');

	// Financial Reports
	Route::middleware('permission:view trial-balance-report')->get('/reports/financial/trial-balance', [FinancialReportsController::class, 'trialBalance'])->name('reports.financial.trial-balance');
	Route::middleware('permission:view balance-sheet-report')->get('/reports/financial/balance-sheet', [FinancialReportsController::class, 'balanceSheet'])->name('reports.financial.balance-sheet');
	Route::middleware('permission:view income-statement-report')->get('/reports/financial/income-statement', [FinancialReportsController::class, 'incomeStatement'])->name('reports.financial.income-statement');
	Route::middleware('permission:view general-ledger-summary')->get('/reports/financial/general-ledger-summary', [FinancialReportsController::class, 'generalLedgerSummary'])->name('reports.financial.general-ledger-summary');
	Route::middleware('permission:view cash-flow-statement')->get('/reports/financial/cash-flow-statement', [FinancialReportsController::class, 'cashFlowStatement'])->name('reports.financial.cash-flow-statement');
	Route::middleware('permission:view income-report')->get('/reports/financial/income-report', [FinancialReportsController::class, 'incomeReport'])->name('reports.financial.income-report');
	Route::middleware('permission:view daily-report')->get('/reports/financial/daily-reconciliation', [TransactionController::class, 'dailyReconciliationReport'])->name('reports.financial.daily-reconciliation-report');
	Route::middleware('permission:view general-ledger-summary')->get('/reports/financial/general-ledger-statement-break-down', [FinancialReportsController::class, 'generalLedgerBreakDown']);
	Route::middleware('permission:view sms-logs')->get('/reports/sms-report', [\App\Http\Controllers\SmsReportController::class, 'index'])->name('reports.sms.index');

	Route::middleware('permission:view asset-locations')->group(function () {
		Route::resource('asset-locations', AssetLocationController::class);
	});
	// Route::resource('cash-sales', CashSaleController::class)
	// 	->middleware(['auth']);
	Route::get('cash-sales', [CashSaleController::class, 'index'])->name('cash-sales.index');
	Route::get('cash-sales/create', [CashSaleController::class, 'create'])->name('cash-sales.create');
	Route::get('cash-sales/edit/{cashSale}', [CashSaleController::class, 'edit'])->name('cash-sales.edit');
	Route::put('cash-sales/update/{cashSale}', [CashSaleController::class, 'update'])->name('cash-sales.update');
	Route::get('cash-sales/bulk-upload', [CashSaleController::class, 'bulkUploadForm'])->name('cash-sales.bulk-upload.form');
	Route::post('cash-sales/bulk-upload', [CashSaleController::class, 'bulkUpload'])->name('cash-sales.bulk-upload');
	Route::middleware('permission:view sms-templates')->group(function () {
		Route::get('/sms-templates', [SmsTemplatesController::class, 'index'])->name('sms-templates.index');
		Route::get('/sms-templates/create', [SmsTemplatesController::class, 'create'])->name('sms-template.create');
		Route::get('/sms-templates/{template}', [SmsTemplatesController::class, 'edit'])->name('sms-template.edit');
		Route::middleware('permission:create sms-templates')->post('/sms-templates/store', [SmsTemplatesController::class, 'store'])->name('sms-template.store');
		Route::middleware('permission:update sms-templates')->put('/sms-templates/{template}', [SmsTemplatesController::class, 'update'])->name('sms-template.update');
		Route::middleware('permission:delete sms-templates')->delete('/sms-templates/{template}', [SmsTemplatesController::class, 'delete'])->name('sms-template.delete');
	});

	// View Switches Index
	Route::middleware('permission:view switches')
		->get('/switches', [SwitchesController::class, 'index'])
		->name('switches.index');

	Route::middleware('permission:create switches')
		->get('/switches/create', [SwitchesController::class, 'create'])
		->name('switches.create');

	Route::middleware('permission:create switches')
		->post('/switches', [SwitchesController::class, 'store'])
		->name('switches.store');

	Route::middleware('permission:update switches')
		->get('/switches/{switch}/edit', [SwitchesController::class, 'edit'])
		->name('switches.edit');

	Route::middleware('permission:update switches')
		->put('/switches/{switch}', [SwitchesController::class, 'update'])
		->name('switches.update');

	Route::middleware('permission:delete switches')
		->delete('/switches/{switch}', [SwitchesController::class, 'destroy'])
		->name('switches.destroy');
	Route::patch('/switches/{switch}/toggle-environment', [SwitchesController::class, 'toggleEnvironment'])->name('switches.toggle-environment');
	Route::patch('/switches/{switch}/toggle-status', [SwitchesController::class, 'toggleStatus'])->name('switches.toggle-status');


	// Loan Reports
	Route::middleware('permission:view loan-report')->group(function () {
		Route::post('/export-csv', [LoanReportsController::class, 'exportToCSV'])->name('export.csv');
		Route::post('/export-pdf', [LoanReportsController::class, 'exportToPDF'])->name('export.pdf');
		Route::get('/reports/loans/disbursement', [LoanReportsController::class, 'disbursement'])->name('reports.loans.disbursement');
		Route::get('/reports/loans/collections', [LoanReportsController::class, 'collections'])->name('reports.loans.collections');
		Route::get('/reports/loans/outstanding', [LoanReportsController::class, 'outstanding'])->name('reports.loans.outstanding');
		Route::get('/reports/loans/paidoff', [LoanReportsController::class, 'paidOff'])->name('reports.loans.paidoff');
		Route::get('/reports/loans/overdue', [LoanReportsController::class, 'overdue'])->name('reports.loans.overdue');
		Route::get('/reports/loans/portfolio-at-risk', [LoanReportsController::class, 'portfolio_at_risk'])->name('reports.loans.portfolio_at_risk');
		Route::get('/reports/loans/arrears-ageing-report', [LoanReportsController::class, 'arrearsAgingReport'])->name('reports.loans.arrears_ageing_report');
		Route::get('/reports/loans/monthly-report', [LoanReportsController::class, 'monthlyReport'])->name('reports.loans.monthly-report');
		Route::get('/reports/loans/loan-product-report', [LoanProductReportConroller::class, 'index'])->name('reports.loans.loan-product-report');
		Route::get('/reports/loans/pending/disbursement', [LoanReportsController::class, 'pendingDisbursements'])->name('reports.loans.pending.disbursement');
		Route::get('/reports/loan-applications', [LoanApplicationReportController::class, 'index'])->name('reports.loanApplications.index');
		Route::get('/reports/loans/rejected/applications', [LoanReportsController::class, 'rejectedApplications'])->name('reports.loans.rejected.applications');
		Route::get('/reports/loans/repayment', [LoanReportsController::class, 'repaymentReport'])->name('reports.loans.repayment-report');
		Route::get('/reports/loans/loss-provisions', [LoanReportsController::class, 'provisionsReport'])->name('reports.loans.provisions-report');
		Route::get('/reports/loans/written-off', [LoanReportsController::class, 'writtenOffReport'])->name('reports.loans.written-off-report');
		Route::get('/reports/loans/written-off-recovered', [LoanReportsController::class, 'writtenOffRecoveredReport'])->name('reports.loans.written-off-recovered-report');
		Route::get('/reports/loans/interest-receivable', [LoanReportsController::class, 'interestReceivable'])->name('reports.loans.interest-receivable');
		Route::get('/reports/loans/penalties-receivable', [LoanReportsController::class, 'penaltiesReceivable'])->name('reports.loans.penalties-receivable');
		Route::get('/due-loans-report', [LoanReportsController::class, 'dueLoansReport'])->name('due-loans-report');
		Route::get('/reports/loans/consolidated', [\App\Http\Controllers\Reports\Exports\ConsolidatedLoansReportController::class, 'index'])->name('reports.loans.consolidated');
		Route::get("/loans-in-arrears-report", [LoanReportsController::class, 'loansInArrearsReport'])->name('loans-in-arrears-report');
		Route::get("/missed-repayments-report", [LoanReportsController::class, 'missedRepayments'])->name('reports.loans.missed-repayments-report');
		Route::get('/reports/savings/deposits', [\App\Http\Controllers\Reports\Exports\DepositReportController::class, 'show'])->name('reports.deposits.show');
		Route::get('/reports/savings/full-payments', [\App\Http\Controllers\Reports\Exports\FullPaymentReportController::class, 'show'])->name('reports.full-payments.show');
		Route::get('/reports/savings/withdrawals', [\App\Http\Controllers\Reports\Exports\WithdrawalReportController::class, 'show'])->name('reports.withdrawals.show');

		Route::get('export/pdf/portfolio-at-risk', [\App\Http\Controllers\Reports\Exports\PortfolioAtRiskReportController::class, 'pdf'])->name('export.portfolio-at-risk.pdf');
		Route::get('export/pdf/paid-off-loan', [\App\Http\Controllers\Reports\Exports\PaidOffLoansReportController::class, 'pdf'])->name('export.paid-off-loans.pdf');
		Route::get('export/pdf/written-off-loans', [\App\Http\Controllers\Reports\Exports\WrittenOffLoansReportController::class, 'pdf'])->name('export.written-off-loans.pdf');
		Route::get('export/pdf/blacklisted-customers', [\App\Http\Controllers\Reports\Exports\BlacklistedCustomerReportController::class, 'pdf'])->name('export.blacklisted-customers.pdf');
		Route::get('export/pdf/pending-disbursements', [\App\Http\Controllers\Reports\Exports\PendingDisbursementReportController::class, 'pdf'])->name('export.pending-disbursement.pdf');
		Route::get('export/pdf/deposits-report', [\App\Http\Controllers\Reports\Exports\DepositReportController::class, 'pdf'])->name('export.deposits.pdf');
		Route::get('export/pdf/full-payments-report', [\App\Http\Controllers\Reports\Exports\FullPaymentReportController::class, 'pdf'])->name('export.full-payments.pdf');
		Route::get('export/pdf/withdrawals-report', [\App\Http\Controllers\Reports\Exports\WithdrawalReportController::class, 'pdf'])->name('export.withdrawals.pdf');
		Route::get('export/pdf/saving-accounts-report', [SavingsReportsController::class, 'pdf'])->name('export.saving-accounts.pdf');
		Route::get('export/pdf/transactions-report', [TransactionController::class, 'pdf'])->name('export.transactions.pdf');
	});

	// Savings Reports
	Route::middleware('permission:view savings-report')->get('/reports/savings/summary', [SavingsReportsController::class, 'show'])->name('reports.savings.summary');

	// SMS Minimum Balance
	Route::middleware('permission:update partners')->put('sms/set-minimum-balance', [SmsController::class, 'setMinimumBalance'])->name('sms.set-minimum-balance');

	// SMS Campaigns
	Route::resource('sms-campaigns', SmsCampaignController::class);
	Route::middleware('permission:view sms-campaigns')->get('/sms-campaigns/customers/{targetGroup}/{partnerId}', [SmsCampaignController::class, 'getCustomers'])->name('sms-campaigns.customers');
	Route::middleware('permission:update partners')->get('sms/create-minimum-balance', [SmsController::class, 'createMinimumBalance'])->name('sms.create-minimum-balance');

	// Roles and Permissions
	Route::middleware('permission:view roles')->group(function () {
		Route::get('roles', [RolePermissionController::class, 'index'])->name('roles.index');
		Route::get('permissions', [RolePermissionController::class, 'permissions'])->name('roles.permissions');
		Route::get('roles-permissions/create-role', [RolePermissionController::class, 'createRole'])->name('roles-permissions.create-role');
		Route::middleware('permission:create roles')->post('roles-permissions/store-role', [RolePermissionController::class, 'storeRole'])->name('roles-permissions.store-role');
		Route::get('roles-permissions/edit-role/{role}', [RolePermissionController::class, 'editRole'])->name('roles-permissions.edit-role');
		Route::middleware('permission:update roles')->put('roles-permissions/update-role/{role}', [RolePermissionController::class, 'updateRole'])->name('roles-permissions.update-role');
		Route::middleware('permission:delete roles')->delete('roles-permissions/delete-role/{role}', [RolePermissionController::class, 'deleteRole'])->name('roles-permissions.delete-role');
	});

	// Other Reports
	Route::middleware('permission:view fees-report')->get('/reports/others/fees-report', [FeesReportController::class, 'index'])->name('reports.others.fees-report');
	Route::middleware('permission:view borrowers-report')->get('/reports/others/borrowers-report', [BorrowersReportController::class, 'index'])->name('reports.borrowers-report');
	Route::middleware('permission:view daily-report')->get('/reports/others/daily-report', [AtAGlanceReportController::class, 'dailyReport'])->name('reports.others.daily-report');
	Route::middleware('permission:view at-a-glance-report')->get('/reports/others/performance-metrics', [AtAGlanceReportController::class, 'atAGlanceReport'])->name('reports.others.at-a-glance-report');
	Route::middleware('permission:view payment-history-velocity-report')->get('/reports/others/payment-history-velocity', [\App\Http\Controllers\PaymentHistoryVelocityReportController::class, 'index'])->name('reports.others.payment-history-velocity');
	Route::middleware('permission:view deferred-income-report')->get('/reports/others/deferred-income-report', [DefferedIncomeReport::class, 'deferredIncomeReport'])->name('reports.others.deferred-income-report');
	Route::middleware('permission:view deferred-monthly-income-report')->get('/reports/others/deferred-monthly-income-report', [DefferedIncomeReport::class, 'deferredMonthlyIncomeReport'])->name('reports.others.deferred-monthly-income-report');

	// Loan Product Addons
	Route::middleware('permission:create loan-products')->post('/loan-product-addons', [LoanProductAddonController::class, 'store'])->name('loan-product-addons.store');
	Route::middleware('permission:update loan-products')->put('/loan-product-addons/{loanProductAddon}', [LoanProductAddonController::class, 'update'])->name('loan-product-addons.update');
	Route::middleware('permission:delete loan-products')->delete('/loan-product-addons/{loanProductAddon}', [LoanProductAddonController::class, 'destroy'])->name('loan-product-addons.destroy');

	Route::middleware('permission:view hits')->get('/hits', [HitsController::class, 'index'])->name('hits.index');
	Route::middleware('permission:create loan-products')->post('/loan/close', [LoanController::class, 'closeLoan'])->name('loan.close');

	// Asset Products
});

// layout
Route::get('/layouts/without-menu', [WithoutMenu::class, 'index'])->name('layouts-without-menu');
Route::get('/layouts/without-navbar', [WithoutNavbar::class, 'index'])->name('layouts-without-navbar');
Route::get('/layouts/fluid', [Fluid::class, 'index'])->name('layouts-fluid');
Route::get('/layouts/container', [Container::class, 'index'])->name('layouts-container');
Route::get('/layouts/blank', [Blank::class, 'index'])->name('layouts-blank');

// pages
Route::get('/pages/account-settings-account', [AccountSettingsAccount::class, 'index'])->name('pages-account-settings-account');
Route::get('/pages/account-settings-notifications', [AccountSettingsNotifications::class, 'index'])->name('pages-account-settings-notifications');
Route::get('/pages/account-settings-connections', [AccountSettingsConnections::class, 'index'])->name('pages-account-settings-connections');
Route::get('/pages/misc-error', [MiscError::class, 'index'])->name('pages-misc-error');
Route::get('/pages/misc-under-maintenance', [MiscUnderMaintenance::class, 'index'])->name('pages-misc-under-maintenance');

// cards
Route::get('/cards/basic', [CardBasic::class, 'index'])->name('cards-basic');

// User Interface
Route::get('/ui/accordion', [Accordion::class, 'index'])->name('ui-accordion');
Route::get('/ui/alerts', [Alerts::class, 'index'])->name('ui-alerts');
Route::get('/ui/badges', [Badges::class, 'index'])->name('ui-badges');
Route::get('/ui/buttons', [Buttons::class, 'index'])->name('ui-buttons');
Route::get('/ui/carousel', [Carousel::class, 'index'])->name('ui-carousel');
Route::get('/ui/collapse', [Collapse::class, 'index'])->name('ui-collapse');
Route::get('/ui/dropdowns', [Dropdowns::class, 'index'])->name('ui-dropdowns');
Route::get('/ui/footer', [Footer::class, 'index'])->name('ui-footer');
Route::get('/ui/list-groups', [ListGroups::class, 'index'])->name('ui-list-groups');
Route::get('/ui/modals', [Modals::class, 'index'])->name('ui-modals');
Route::get('/ui/navbar', [Navbar::class, 'index'])->name('ui-navbar');
Route::get('/ui/offcanvas', [Offcanvas::class, 'index'])->name('ui-offcanvas');
Route::get('/ui/pagination-breadcrumbs', [PaginationBreadcrumbs::class, 'index'])->name('ui-pagination-breadcrumbs');
Route::get('/ui/progress', [Progress::class, 'index'])->name('ui-progress');
Route::get('/ui/spinners', [Spinners::class, 'index'])->name('ui-spinners');
Route::get('/ui/tabs-pills', [TabsPills::class, 'index'])->name('ui-tabs-pills');
Route::get('/ui/toasts', [Toasts::class, 'index'])->name('ui-toasts');
Route::get('/ui/tooltips-popovers', [TooltipsPopovers::class, 'index'])->name('ui-tooltips-popovers');
Route::get('/ui/typography', [Typography::class, 'index'])->name('ui-typography');

// extended ui
Route::get('/extended/ui-perfect-scrollbar', [PerfectScrollbar::class, 'index'])->name('extended-ui-perfect-scrollbar');
Route::get('/extended/ui-text-divider', [TextDivider::class, 'index'])->name('extended-ui-text-divider');

// icons
Route::get('/icons/boxicons', [Boxicons::class, 'index'])->name('icons-boxicons');

// form elements
Route::get('/forms/basic-inputs', [BasicInput::class, 'index'])->name('forms-basic-inputs');
Route::get('/forms/input-groups', [InputGroups::class, 'index'])->name('forms-input-groups');

// form layouts
Route::get('/form/layouts-vertical', [VerticalForm::class, 'index'])->name('form-layouts-vertical');
Route::get('/form/layouts-horizontal', [HorizontalForm::class, 'index'])->name('form-layouts-horizontal');

// tables
Route::get('/tables/basic', [TablesBasic::class, 'index'])->name('tables-basic');
