<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\FloatTopUp;
use Illuminate\Http\Request;
use App\Models\Accounts\Account;
use App\Models\User;
use App\Notifications\NewFloatTopupNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Services\Account\AccountSeederService;

class FLoatTopUpController extends Controller
{
    public function index(Request $request)
    {
        $total_float_balance = 0;

        $pending_topups = FloatTopUp::where('Status', 'Pending')
            ->orderByDesc("id")
            ->get();

        $disbursement_ova = Account::where('partner_id', Auth::user()->partner_id)
            ->where('slug', AccountSeederService::DISBURSEMENT_OVA_SLUG)
            ->first();
        if ($disbursement_ova) {
            $total_float_balance = $disbursement_ova->balance;
        }
        return view('float-topup.index', compact('pending_topups', 'total_float_balance',));
    }

    /**
     * Store a new float top-up in the database.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'Amount' => 'required|numeric|min:0',
                'Proof_Of_Payment' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            // Handle the file upload
            if ($request->hasFile('Proof_Of_Payment')) {
                $filePath = $request->file('Proof_Of_Payment')->store('public/proofs_of_payment');
                $proofOfPaymentPath = Storage::url($filePath);
            }

            // Create the float top-up record
            FloatTopUp::create([
                'Partner_ID' => Auth::user()->partner_id,
                'Amount' => $validatedData['Amount'],
                'Proof_Of_Payment' => $proofOfPaymentPath,
            ]);
            $user = User::find(1); // The user to notify
            $user->notify(new NewFloatTopupNotification());
            // Redirect with success message
            return redirect()->back()->with('success', 'Float top-up submitted successfully.');
        } catch (\Throwable $th) {
            // Redirect with error message
            return redirect()->back()->with('error', 'Failed to submit float top-up. Error: ' . $th->getMessage());
        }
    }

    public function approve(FloatTopUp $topup)
    {
        DB::beginTransaction();
        try {
            if (!$topup->update(['Status' => 'Approved'])) {
                throw new Exception('Failed to approve float top-up', 500);
            }
            $topup->affectAccounts();
            DB::commit();
            return redirect()->back()->with('success', 'Float top-up approved successfully.');
        } catch (\Throwable $th) {
            DB::rollBack();
            return redirect()->back()->with('error', $th->getMessage());
        }
    }
}
