<?php

namespace App\Console\Commands\Tests;

use App\Services\IdentityValidationService;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class Authenticate extends Command
{
  protected $signature = 'nin:authenticate';
  protected $description = 'Get access token for nin verification';

  public function handle(): int
  {
    $api = app(IdentityValidationService::class);

    try {
      $this->info($api->authenticate());

      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
