<?php

namespace App\Http\Controllers;

use App\Models\LoanApplication;
use Illuminate\Http\Request;

class DownpaidLoanApplicationController extends Controller
{
    public function index()
    {
        $loanApplications = LoanApplication::query()
            ->has('successfullDownPayment')
            ->latest()
            ->paginate(25);

        return view('downpayments.index', compact('loanApplications'));
    }

    public function show(LoanApplication $loanApplication)
    {
      return view('downpayments.show', compact('loanApplication'));
    }
}
