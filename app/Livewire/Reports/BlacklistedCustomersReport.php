<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetBlacklistedCustomerReportDetailsAction;
use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Exports\BlackListedCustomersExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class BlacklistedCustomersReport extends Component
{
    use ExportsData, WithPagination;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.blacklisted-customer-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.blacklisted-customers', [
                'records' => app(GetBlacklistedCustomerReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters(),
            ])
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new BlackListedCustomersExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetBlacklistedCustomerReportDetailsAction::class)
            ->paginate()
            ->filters([
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
            ])
            ->execute();
    }
}
