<?php

namespace App\Jobs;

use App\Contracts\SmsProviderInterface;
use App\Models\SavingsAccount;
use App\Models\SavingsProduct;
use App\Models\SmsLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Number;
use function Termwind\ask;

class SendSms //implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $maxExceptions = 3;
    public int $timeout = 300; // 5 minutes
    public array $backoff = [60, 180, 360]; // Retry after 1, 3, 6 minutes

    protected string $defaultMessage = 'Feel the power of Electric Motorcycles with SPIRO! Get one from as low as UGX 600k. Dial *185*8*9# to explore options & apply. Book now, ride now. T&Cs apply.';

    protected string $customMessage = 'Thank you for paying UGX :currentBalance Deposit UGX :targetAmount to get the Spiro electric motorcycle on credit or keep paying UGX 10K daily. Dial *185*8*9# to continue booking.';

    protected int $partnerId;

    public function __construct(
        private readonly array  $phoneNumbers,
    ) {
    }

    public function handle(SmsProviderInterface $smsProvider): void
    {
        $result = $smsProvider->sendBulk($this->generateMessages(), $this->defaultMessage);
        //$result = ['success' => true, 'results' => []];
        $bulkDetails = [
            'partner_id' => $this->partnerId,
            'telephone_numbers' => implode(',', $this->phoneNumbers),
            'bulk_cost' => data_get($result, 'results.Cost', 0),
            'bulk_count' => count($this->phoneNumbers),
            'reference_id' => data_get($result, 'results.MsgFollowUpUniqueCode'), // todo: Unify response so that this is usable for other providers.
            'created_at' => now(),
            'updated_at' => now(),
        ];

        if (! $result['success']) {
            Log::error('Bulk SMS sending failed');

            if ($this->attempts() >= $this->tries) {
                // Log final failure for manual review
                Log::critical('Bulk SMS final attempt failed');

                DB::table('bulk_sms_logs')
                    ->insert(array_merge($bulkDetails, [
                        'status' => 'Failed',
                        'status_message' => $result['error'] ?? 'Unknown error',
                    ]));
            } else {
                $this->release($this->backoff[$this->attempts() - 1]);
            }

            return;
        }

        DB::table('bulk_sms_logs')->insert(array_merge($bulkDetails, ['status' => 'Completed']));
    }

    public function setPartner($partnerId)
    {
        $this->partnerId = $partnerId;

        return $this;
    }

    public function setCustomMessage(string $message): self
    {
        $this->customMessage = $message;

        return $this;
    }

    public function setDefaultMessage(string $message): self
    {
        $this->defaultMessage = $message;

        return $this;
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Bulk SMS job failed');
    }

    /**
     * @return array|array[]
     */
    public function generateMessages(): array
    {
        $minimumDepositAmount = SavingsProduct::query()->select('minimum_deposit')->firstWhere('partner_id', $this->partnerId)?->minimum_deposit;
        // Get phone numbers that are in this batch and also already have saving accounts with current balance
        $existingSavingAccounts = DB::table('savings_accounts as sa')
            ->selectRaw('c.telephone_number, sa.current_balance')
            ->join('customers as c', 'sa.customer_id', '=', 'c.id')
            ->where('sa.is_active', 1)
            ->where('sa.current_balance', '>=', 10000)
            ->where('sa.current_balance', '<', $minimumDepositAmount)
            ->whereIn('c.telephone_number', $this->phoneNumbers)
            ->get();

        return $existingSavingAccounts->map(function ($savingAccount) use ($minimumDepositAmount) {
            return [
                'phone' => $savingAccount->telephone_number,
                'message' => str($this->customMessage)
                    ->replace(':currentBalance', $this->abbreviateNumber($savingAccount->current_balance))
                    ->replace(':targetAmount', $this->abbreviateNumber($minimumDepositAmount - $savingAccount->current_balance))
                    ->toString(),
            ];
        })->merge(
            array_map(function ($phone) {
                return [
                    'phone' => $phone,
                    'message' => $this->defaultMessage,
                ];
            }, array_diff($this->phoneNumbers, $existingSavingAccounts->pluck('telephone_number')->toArray()))
        )->toArray();
    }

    protected function abbreviateNumber($number): string
    {
        return Number::abbreviate($number);
    }
}
