@extends('pdf.layouts')

@section('content')
    <div class="text-center">
        <h2 style="margin-bottom: 5px; margin-top: 0; font-size: 16px">{{ $partnerName }}</h2>
        <h4 style="margin-top: 0; margin-bottom: 4px">Arrears Report</h4>
        <p style="margin-top: 0; font-size: 10px">As at: {{ $filters['endDate'] }}</p>
    </div>

    <table id="report-table" class="table table-bordered">
        <thead>
            <tr class="table-header">
                <th colspan="4"></th>
                <th colspan="3" class="text-center">Outstanding</th>
                <th colspan="3" class="text-center">Arrears</th>
                <th colspan="3"></th>
            </tr>
            <tr>
                <th>Loan #</th>
                <th>Customer</th>
                <th>Phone Number</th>
                <th class="text-end">Amount Disbursed</th>
                <th class="text-end">Principal</th>
                <th class="text-end">Interest</th>
                <th class="text-end">Penalty</th>
                <th class="text-end">Total Balance</th>
                <th class="text-end">Principal</th>
                <th class="text-end">Interest</th>
                <th class="text-end">Penalty</th>
                <th class="text-end">Total Arrears</th>
                <th class="text-end">Arrears Rate</th>
                <th class="text-end">Days in Arrears</th>
                <th class="text-end">Expiry Date</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($loans as $loan)
                <tr>
                    <td>{{ $loan->id }}</td>
                    <td>{{ $loan->customer->name }}</td>
                    <td>{{ $loan->customer->Telephone_Number }}</td>
                    <td class="text-end"><x-money :value="$loan->Facility_Amount_Granted" /></td>
                    <td class="text-end"><x-money :value="$loan->schedule_sum_principal_remaining" /></td>
                    <td class="text-end"><x-money :value="$loan->schedule_sum_interest_remaining" /></td>
                    <td class="text-end"><x-money :value="$loan->penalty_amount" /></td>
                    <td class="text-end"><x-money :value="$loan->total_outstanding_amount" /></td>
                    <td class="text-end"><x-money :value="$loan->total_principal_arrears" /></td>
                    <td class="text-end"><x-money :value="$loan->total_interest_arrears" /></td>
                    <td class="text-end"><x-money :value="$loan->penalty_arrears" /></td>
                    <td class="text-end"><x-money :value="$loan->total_arrears_amount" /></td>
                    <td class="text-end">{{ $loan->arrears_rate }}%</td>
                    <td class="text-end">{{ $loan->arrear_days < 0 ? abs($loan->arrear_days) : 0 }}</td>
                    <td class="text-end">{{ $loan->Maturity_Date->format('d-m-Y') }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="15" class="text-center">No records found</td>
                </tr>
            @endforelse
        </tbody>
        <tfoot>
            <tr>
                <th>Totals</th>
                <th class="text-end">{{ count($loans) }}</th>
                <th></th>
                <th class="text-end"><x-money :value="$loans->sum('Facility_Amount_Granted')" /></th>
                <th class="text-end">{{ number_format($loans->sum('schedule_sum_principal_remaining')) }}</th>
                <th class="text-end">{{ number_format($loans->sum('schedule_sum_interest_remaining')) }}</th>
                <th class="text-end">{{ number_format($loans->sum('penalty_amount')) }}</th>
                <th class="text-end">{{ number_format($loans->sum('total_outstanding_amount')) }}</th>
                <th class="text-end">{{ number_format($loans->sum('total_principal_arrears')) }}</th>
                <th class="text-end">{{ number_format($loans->sum('total_interest_arrears')) }}</th>
                <th class="text-end">{{ number_format($loans->sum('penalty_arrears')) }}</th>
                <th class="text-end">{{ number_format($loans->sum('total_arrears_amount')) }}</th>
                <th></th>
                <th></th>
                <th></th>
            </tr>
        </tfoot>
    </table>
    <x-print-footer />
@endsection
