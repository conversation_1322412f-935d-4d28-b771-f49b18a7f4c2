<?php

namespace App\Http\Controllers\DecisionEngine;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Partner;
use App\Models\DecisionEngine\RiskMultiplier;
use App\Models\DecisionEngine\RiskClass;
use App\Validators\UniquePartnerMinMax;
use App\Services\DecisionEngineService;
use Illuminate\Foundation\Validation\ValidatesRequests;

class RiskMultiplierController extends Controller
{
    use ValidatesRequests;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return view("decision-engine.risk-multipliers.index");
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try {
            $partnerId = auth()->user()->partner_id;
            $partners = is_null($partnerId)
                ? Partner::orderBy("Institution_Name", "asc")
                    ->get()
                : Partner::whereId($partnerId)
                    ->get();
            $risk_classes = RiskClass::orderBy("class", "asc")->orderBy("type", "asc")->get();

            return view("decision-engine.risk-multipliers.create", compact("partners", "risk_classes"));
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $rules = [
            'partner_id' => 'nullable',
            'risk_class' => 'required',
            'multiplier' => 'required|numeric',
            'partner_id' => [
                'nullable',
                function ($attribute, $value, $fail) use ($request) {
                    $exists = RiskMultiplier::where('partner_id', $value)
                        ->where('risk_class', $request->input('risk_class'))
                        ->where('multiplier', $request->input('multiplier'))
                        ->exists();

                    if ($exists) {
                        $fail('The combination of partner, risk class, and multiplier must be unique.');
                    }
                },
            ],
        ];

        $customMessages = [
            'risk_class.required' => 'Risk class is required.',
            'multiplier.required' => 'The multiplier is required.',
            'multiplier.numeric' => 'The multiplier must be a number.',
        ];

        $this->validate($request, $rules, $customMessages);

        try {
            $data = $request->all();

            RiskMultiplier::create($data);

            return redirect()
                ->route("decision-engine.risk-multipliers.index")
                ->withSuccess("Multiplier created successfully");
        } catch (\Throwable $r) {
            return redirect()->back()->withErrors($r->getMessage())->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    { }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $data = RiskMultiplier::find($id);
            $partnerId = auth()->user()->partner_id;
            $partners = is_null($partnerId)
                ? Partner::orderBy("Institution_Name", "asc")
                    ->get()
                : Partner::whereId($partnerId)
                    ->get();

            return view("decision-engine.risk-multipliers.edit", compact("partners", "data"));
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $rules = [
            'partner_id' => 'nullable',
            'risk_class' => 'required',
            'multiplier' => 'required|numeric',
            'partner_id' => [
                'nullable',
                function ($attribute, $value, $fail) use ($request, $id) {
                    $exists = RiskMultiplier::where('partner_id', $value)
                        ->where('risk_class', $request->input('risk_class'))
                        ->where('multiplier', $request->input('multiplier'))
                        ->where('id', '!=', $id) // Exclude the current record from the check
                        ->exists();

                    if ($exists) {
                        $fail('The combination of partner, risk class, and multiplier must be unique.');
                    }
                },
            ],
        ];

        $customMessages = [
            'risk_class.required' => 'Risk class is required.',
            'multiplier.required' => 'The multiplier is required.',
            'multiplier.numeric' => 'The multiplier must be a number.',
        ];

        $this->validate($request, $rules, $customMessages);

        try {
            $data = $request->only(['partner_id', 'risk_class_id', 'multiplier']);

            // Update the existing record
            RiskMultiplier::where('id', $id)->update($data);

            return redirect()
                ->route("decision-engine.risk-multipliers.index")
                ->withSuccess("Multiplier updated successfully");
        } catch (\Throwable $r) {
            return redirect()->back()->withErrors($r->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $data = RiskMultiplier::findOrFail($id);
            $data->delete();
            return redirect()
                ->back()
                ->withSuccess("Multiplier deleted successfully");
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }
}
