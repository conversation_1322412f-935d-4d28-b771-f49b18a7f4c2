<?php

namespace App\Actions\Loans;

use App\Enums\LoanAccountType;
use App\Models\Accounts\Account;
use App\Models\JournalEntry;
use App\Models\Loan;
use App\Models\LoanLossProvision as LoanLossProvisionModel;
use App\Models\LoanRepayment;
use App\Models\Transaction;
use App\Services\Account\AccountSeederService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ProcessLoanRepaymentAction
{
    public function execute(Transaction $transaction): bool
    {
        try {
            $transaction->loadMissing(['loan', 'customer']);

            $customer = $transaction->customer;

            if (!$customer) {
                throw new \Exception('Customer that performed the transaction was not found. Of they changed their phone number.');
            }

            $loan = $transaction->loan;

            if (!$loan) {
                throw new \Exception('Loan that performed the transaction was not found. Of they changed their phone number.');
            }

            $repayment = LoanRepayment::createPayment($loan, $transaction->Amount);

            if ($loan->isWrittenOff()) {
                return $this->recoverWrittenOffLoan($transaction, $repayment);
            }

            $repayment->affectAccounts();
            return true;
        } catch (\Exception $e) {
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    protected function recoverWrittenOffLoan(Transaction $transaction, LoanRepayment $repayment): bool
    {
        $transaction->loadMissing(['loan', 'customer']);
        $collectionAccount = Account::query()
            ->where('slug', AccountSeederService::COLLECTION_OVA_SLUG)
            ->where('partner_id', $transaction->Partner_ID)
            ->first();
        $recoveriesFromWrittenOffLoansAccount = Account::query()
            ->where('slug', AccountSeederService::RECOVERIES_FROM_WRITTEN_OFF_LOANS_SLUG)
            ->where('partner_id', $transaction->Partner_ID)
            ->first();

        if (empty($collectionAccount) || empty($recoveriesFromWrittenOffLoansAccount)) {
            Log::error('Missing collection or loan recoveries account');

            return false;
        }

        $transaction->loan()->update([
            'Written_Off_Amount_Recovered' => $transaction->loan->loan_repayments()
                ->where('Credit_Account_Status', LoanAccountType::WrittenOff)
                ->sum('amount'),
            'Last_Recovered_At' => now()
        ]);

        $now = now();
        $journalEntries = [];
        $txn_id = rand(11111, 99999) . "-" . $now->unix();

        // Debit Record
        $currentBalance = $collectionAccount->balance + $transaction->Amount;
        $journalEntries[] = [
            'account_id' => $collectionAccount->id,
            'amount' => $transaction->Amount,
            'transactable_id' => $repayment->id,
            'transactable' => LoanRepayment::class,
            'partner_id' => $transaction->Partner_ID,
            'txn_id' => $txn_id,
            'account_name' => $collectionAccount->name,
            'cash_type' => 'Cash In',
            'previous_balance' => $collectionAccount->balance,
            'current_balance' => $currentBalance,
            'accounting_type' => 'Debit',
            'credit_amount' => 0,
            'debit_amount' => $transaction->Amount,
            'created_at' => $now,
            'updated_at' => $now,
        ];
        $collectionAccount->balance = $currentBalance;
        $collectionAccount->save();

        $currentBalance = $recoveriesFromWrittenOffLoansAccount->balance + $transaction->Amount;
        // Credit Record
        $journalEntries[] = [
            'account_id' => $recoveriesFromWrittenOffLoansAccount->id,
            'amount' => $transaction->Amount,
            'transactable_id' => $repayment->id,
            'transactable' => LoanRepayment::class,
            'partner_id' => $transaction->Partner_ID,
            'txn_id' => $txn_id,
            'account_name' => $recoveriesFromWrittenOffLoansAccount->name,
            'cash_type' => 'Cash In',
            'previous_balance' => $recoveriesFromWrittenOffLoansAccount->balance,
            'current_balance' => $currentBalance,
            'accounting_type' => 'Credit',
            'credit_amount' => $transaction->Amount,
            'debit_amount' => 0,
            'created_at' => $now,
            'updated_at' => $now,
        ];

        $recoveriesFromWrittenOffLoansAccount->balance = $currentBalance;
        $recoveriesFromWrittenOffLoansAccount->save();

        JournalEntry::query()->insert($journalEntries);

        return true;
    }
}
