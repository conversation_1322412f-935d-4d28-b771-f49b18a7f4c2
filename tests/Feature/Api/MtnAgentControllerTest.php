<?php

namespace Tests\Feature\Api;

use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Enums\LoanAccountType;
use App\Models\Loan;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Carbon\Carbon;
use Tests\TestCase;
use App\Models\Customer;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\DB;

class MtnAgentControllerTest extends TestCase
{
    use DatabaseTransactions, WithFaker;

    public function setUp(): void
    {
        parent::setUp();
        Queue::fake();
    }

    // @REGISTER_CUSTOMER
    public function test_it_can_register_a_new_customer()
    {
        $first_name = $this->faker->firstName();
        $last_name = $this->faker->lastName();
        $dob = $this->faker->date();

        $xmlPayload =
            '<?xml version="1.0" encoding="UTF-8"?>'
            . '<ns0:customerregistrationrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">'
            . '<resource>FRI:************/MSISDN</resource>'
            . '<name>'
                . '<firstname>'. $first_name .'</firstname>'
                . '<lastname>'. $last_name .'</lastname>'
            . '</name>'
            . '<gender>Male</gender>'
            . '<dob>'. $dob. '</dob>'
            . '<idtype>NRIN</idtype>'
            . '<idnumber>NATIONAL_ID_NUMBER</idnumber>'
            . '<idexpiry>2024-11-30+03:00</idexpiry>'
            . '<addresses/>'
            . '</ns0:customerregistrationrequest>';

        $response = $this->call(
            'POST',
            '/api/v1/customerregistration',
            [], // Parameters
            [], // Cookies
            [], // Files
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        $response->assertStatus(200);

        $decoded = html_entity_decode($response->getContent());
        $this->assertStringContainsString('<status>PENDING</status>', $decoded);

        $last_customer = Customer::where('Telephone_Number', '************')->first();
        $this->assertEquals($first_name, $last_customer->First_Name);
        $this->assertEquals($last_name, $last_customer->Last_Name);
    }

    public function test_it_does_not_create_customer_when_there_are_validation_errors(){
        $first_name = $this->faker->firstName();
        $last_name = $this->faker->lastName();
        $dob = $this->faker->date();

        $xmlPayload =
            '<?xml version="1.0" encoding="UTF-8"?>'
            . '<ns0:customerregistrationrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">'
            . '<resource>FRI:************/MSISDN</resource>'
            . '<name>'
                . '<firstname>'. $first_name .'</firstname>'
                . '<lastname>'. $last_name .'</lastname>'
            . '</name>'
            . '<dob>'. $dob. '</dob>'
            . '<idtype>NRIN</idtype>'
            . '<idnumber>NATIONAL_ID_NUMBER</idnumber>'
            . '<idexpiry>2024-11-30+03:00</idexpiry>'
            . '<addresses/>'
            . '</ns0:customerregistrationrequest>';

        $response = $this->call(
            'POST',
            '/api/v1/customerregistration',
            [], // Parameters
            [], // Cookies
            [], // Files
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        $decoded = html_entity_decode($response->getContent());

        $this->assertStringContainsString('<errorcode>ACCOUNT_NOT_CREATED</errorcode>', $decoded);

        $this->assertNull(Customer::where('Telephone_Number', '************')->first());
    }

    public function test_returns_unregistered_response_when_customer_not_found()
    {
        // Arrange
        $xmlContent = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:************/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlContent
        );

        $response->assertStatus(200);
        $content = $response->content();

        // Assert
        $this->assertStringContainsString('<status>UNREGISTERED</status>', $content);
        $this->assertStringContainsString('<savingsaccounts/>', $content);
        $this->assertStringContainsString('<loanaccounts/>', $content);
    }

    /**
     * Customer exists in the system but has not opted in.
     *
     * @return void
     */
    public function test_returns_unregistered_response_when_customer_has_no_optional_savings_account()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
            'options' => []
        ]);

        $payload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:' . $customer->Telephone_Number . '/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $payload
        );
        $content = $response->content();

        // Assert
        $response->assertStatus(200);
        $this->assertStringContainsString('<status>UNREGISTERED</status>', $content);
        $this->assertStringContainsString('<savingsaccounts/>', $content);
        $this->assertStringContainsString('<loanaccounts/>', $content);
    }

    public function test_returns_registered_response_without_active_loans()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
            'options' => [
                'savingsaccount' => [
                    'accountnumber' => '***********',
                    'status' => 'ACTIVE',
                    'balance' => [
                        'amount' => 5000,
                        'currency' => 'UGX'
                    ],
                    'savingsaccounttype' => 'SAVINGS'
                ]
            ]
        ]);

        $payload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:' . $customer->Telephone_Number . '/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $payload
        );
        $content = $response->content();

        // Assert
        $response->assertStatus(200);
        $this->assertStringContainsString('<status>REGISTERED</status>', $content);
        $this->assertStringContainsString('<accountnumber>***********</accountnumber>', $content);
    }

    public function test_returns_registered_response_with_active_loan_details()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
            'options' => [
                'savingsaccount' => [
                    'accountnumber' => '***********',
                    'status' => 'ACTIVE',
                    'balance' => [
                        'amount' => 5000,
                        'currency' => 'UGX'
                    ],
                    'savingsaccounttype' => 'SAVINGS'
                ],
                'loanaccounts' => [
                    'loanaccount' => [
                        'accountnumber' => 'L***********'
                    ]
                ]
            ]
        ]);

        $loan = Loan::factory()->createQuietly([
            'Customer_ID' => $customer->id,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value,
            'Credit_Amount' => 100000
        ]);

        // Mock the totalOutstandingBalance method
        $this->partialMock(Loan::class, function ($mock) {
            $mock->shouldReceive('totalOutstandingBalance')->andReturn(100000);
        });

        $payload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:' . $customer->Telephone_Number . '/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $payload
        );
        $content = $response->content();

        // Assert
        $this->assertStringContainsString('<status>REGISTERED</status>', $content);
        $this->assertStringContainsString('<accountnumber>***********</accountnumber>', $content);
        $this->assertStringContainsString('<duedate>' . $loan->Maturity_Date->toDateString() . '</duedate>', $content);
    }

}
