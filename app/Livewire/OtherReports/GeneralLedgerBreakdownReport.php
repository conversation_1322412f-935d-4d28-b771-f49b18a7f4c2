<?php

namespace App\Livewire\OtherReports;

use App\Actions\OtherReports\GetDailyReconciliationReportDetailsAction;
use App\Actions\OtherReports\GetGeneralLedgerBreakdownDetailsAction;
use App\Actions\OtherReports\GetGeneralLedgerSummaryDetailsAction;
use App\Actions\Reports\GetPerformanceMetricsReportDetailsAction;
use App\Exports\GeneralLedgerBreakdownExport;
use App\Exports\PerformanceMetricsExport;
use App\Models\Accounts\Account;
use App\Models\JournalEntry;
use App\Services\Account\AccountSeederService;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class GeneralLedgerBreakdownReport extends Component
{
    use WithPagination, ExportsData;

    public ?int $accountId = null;

    public function mount()
    {
        $this->startDate = now()->subDay()->toDateString();
        $this->endDate = now()->toDateString();
    }

    public function render()
    {
        return view('livewire.reports.general-ledger-breakdown', $this->getViewData());
    }

    public function printReport()
    {
        // Get data to pass to the report
        $viewData = $this->getViewData();
        $viewData['filters'] = $this->getFormattedDateFilters();
        $viewData['partnerName'] = auth()->user()?->partner->Institution_Name;

        return app(PdfGeneratorService::class)
            ->view('pdf.general-ledger-breakdown', $viewData)
            ->streamFromLivewire();
    }

    public function excelExport(): BinaryFileResponse
    {
        return Excel::download(
            new GeneralLedgerBreakdownExport($this->getFiltersWithAdditionalData()),
            $this->getExcelFilename()
        );
    }

    private function getReportData()
    {
        if (empty($this->endDate)) {
            return collect();
        }


        return app(GetGeneralLedgerBreakdownDetailsAction::class)
            ->paginate()
            ->filters($this->getFiltersWithAdditionalData())
            ->execute();
    }
    protected function getFiltersWithAdditionalData(): array
    {
        $filters = $this->getFilters();
        $filters['accountId'] = $this->accountId;

        return $filters;
    }

    protected function getViewData(): array
    {
        $records = $this->getReportData();
        $accounts = JournalEntry::query()
            ->selectRaw('distinct account_name, account_id')
            ->pluck('account_name', 'account_id');

        return [
            'records' => $records,
            'accounts' => $accounts,
        ];
    }
}
