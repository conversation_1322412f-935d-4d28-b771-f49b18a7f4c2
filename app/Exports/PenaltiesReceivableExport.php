<?php

namespace App\Exports;

use App\Actions\Reports\GetInterestReceivableReportDetailsAction;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;

class PenaltiesReceivableExport implements FromView, WithTitle
{
    public function __construct(protected array $filters) {}

    /**
     * @return Collection
     */
    public function view(): \Illuminate\Contracts\View\View
    {
        $records = app(GetInterestReceivableReportDetailsAction::class)->filters($this->filters)->execute();

        return view('excel.penalties-receivable-report', [
            'records' => $records,
            'partnerName' => auth()->user()?->partner->Institution_Name,
            'filters' => $this->filters,
        ]);
    }

    public function title(): string
    {
        return 'Penalties Receivable';
    }
}
