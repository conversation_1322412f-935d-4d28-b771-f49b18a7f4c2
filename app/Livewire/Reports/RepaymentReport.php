<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Exports\RepaymentExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class RepaymentReport extends Component
{
    use ExportsData;

    public function mount()
    {
        $this->startDate = now()->subMonth()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.repayment-report', [
            'records' => $this->getRecords()
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.loan-repayments', [
                'records' => app(GetRepaymentReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters(),
            ])
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new RepaymentExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getRecords()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetRepaymentReportDetailsAction::class)
            ->filters($this->getFilters())
            ->execute();
    }
}
