<?php

namespace App\Services;

use App\Services\Contracts\ProvidesTransactableAPIs;

// TODO - Implement core banking access logic here.
class BankApiService #implements ProvidesTransactableAPIs
{
    public function __construct()
    {
        // Get and set bank auth details
    }

    /**
     * Initiate a collection on the bank's api.
     *
     * @param string $phone_number
     * @param float $amount
     * @param string $txn_reference
     * @param string $reason
     * @return array
     */
    public function collect($phone_number, $amount, $txn_reference, $reason = "Deposit"): array
    {
        // TODO - Implement corebankig access logic here.
        return [];
    }

    /**
     * Initiate a disbursement on the bank's api.
     *
     * @param string $phone_numner
     * @param float $amount
     * @param string $txn_reference
     * @param string $reason
     * @return array
     */
    public function disburse($phone_numner, $amount, $txn_reference, $reason = "Withdraw"): array
    {
        // TODO - Implement corebankig access logic here.
        return [];
    }

    /**
     * Retrieve the status of a collection initiated on the bank's api.
     *
     * @param string $txn_id
     * @return array
     */
    public function collectionStatus($txn_id): array
    {
        // TODO - Implement corebankig access logic here.
        return [];
    }

    /**
     * Retrieve the status of a disbursment initiated on the bank's api.
     *
     * @param string $txn_id
     * @return array
     */
    public function disbursementStatus($txn_id): array
    {
        // TODO - Implement corebankig access logic here.
        return [];
    }
}
