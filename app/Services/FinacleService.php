<?php

namespace App\Services;

use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class FinacleService
{
    /**
     * Execute the Finacle loan disbursement API
     *
     * @param float $amount
     * @param string $phoneNumber
     * @param string $name
     * @param string $disbursementDate
     * @return array
     */
    public function executeLoanDisbursement(float $amount, string $phoneNumber, string $name, string $disbursementDate, int $loanId, string $transactionReference): array
    {
        $url = env('ABC_API_URL');
        $coldate = Carbon::now()->format('Y-m-d');
        $requestID = Str::uuid();
        // Prepare the XML request
        $xmlRequest = <<<XML
        <?xml version="1.0"?>
        <FIXML xsi:schemaLocation="http://www.finacle.com/fixml executeFinacleScript.xsd"
            xmlns="http://www.finacle.com/fixml"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Header>
                <RequestHeader>
                    <MessageKey>
                        <RequestUUID>{$requestID}</RequestUUID>
                        <ServiceRequestId>executeFinacleScript</ServiceRequestId>
                        <ServiceRequestVersion>10.2</ServiceRequestVersion>
                        <ChannelId>COR</ChannelId>
                        <LanguageId/>
                    </MessageKey>
                    <RequestMessageInfo>
                        <BankId>01</BankId>
                        <TimeZone/>
                        <EntityId/>
                        <EntityType/>
                        <ArmCorrelationId/>
                        <MessageDateTime>{$disbursementDate}</MessageDateTime>
                    </RequestMessageInfo>
                    <Security>
                        <Token>
                            <PasswordToken>
                                <UserId/>
                                <Password/>
                            </PasswordToken>
                        </Token>
                        <FICertToken/>
                        <RealUserLoginSessionId/>
                        <RealUser/>
                        <RealUserPwd/>
                        <SSOTransferToken/>
                    </Security>
                </RequestHeader>
            </Header>
            <Body>
                <executeFinacleScriptRequest>
                    <ExecuteFinacleScriptInputVO>
                        <requestId>mobi_loan_disb.scr</requestId>
                    </ExecuteFinacleScriptInputVO>
                    <executeFinacleScript_CustomData>
                        <DISB_AMT>{$amount}</DISB_AMT>
                        <PHONE_NUMBER>{$phoneNumber}</PHONE_NUMBER>
                        <NAME>{$name}</NAME>
                        <DISB_DATE>{$coldate}</DISB_DATE>
                        <LOAN_ID>{$loanId}</LOAN_ID>
                        <TRAN_REF_NUM>{$transactionReference}</TRAN_REF_NUM>
                    </executeFinacleScript_CustomData>
                </executeFinacleScriptRequest>
            </Body>
        </FIXML>
        XML;

        try {
            // Send the request
            $response = Http::withOptions([
                'verify' => false,
            ])
                ->withHeaders([
                    'Content-Type' => 'text/xml',
                ])
                ->withBody($xmlRequest, 'text/xml')
                ->post($url);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'response' => $response->body(),
                ];
            } else {
                Log::error('Finacle API request failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => 'API request failed with status: ' . $response->status(),
                    'response' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Finacle API exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function executeLoanCollection(float $principalAmount, float $normalInterest, float $interestIncome, float $penalInterest, string $phoneNumber, string $name, string $disbursementDate, int $loanId, string $transactionReference): array
    {
        $url = env('ABC_API_URL');
        $coldate = Carbon::now()->format('Y-m-d');
        $requestID = Str::uuid();
        // Prepare the XML request
        $xmlRequest = <<<XML
        <?xml version="1.0"?>
        <FIXML xsi:schemaLocation="http://www.finacle.com/fixml executeFinacleScript.xsd"
            xmlns="http://www.finacle.com/fixml"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Header>
                <RequestHeader>
                    <MessageKey>
                        <RequestUUID>{$requestID}</RequestUUID>
                        <ServiceRequestId>executeFinacleScript</ServiceRequestId>
                        <ServiceRequestVersion>10.2</ServiceRequestVersion>
                        <ChannelId>COR</ChannelId>
                        <LanguageId/>
                    </MessageKey>
                    <RequestMessageInfo>
                        <BankId>01</BankId>
                        <TimeZone/>
                        <EntityId/>
                        <EntityType/>
                        <ArmCorrelationId/>
                        <MessageDateTime>{$disbursementDate}</MessageDateTime>
                    </RequestMessageInfo>
                    <Security>
                        <Token>
                            <PasswordToken>
                                <UserId/>
                                <Password/>
                            </PasswordToken>
                        </Token>
                        <FICertToken/>
                        <RealUserLoginSessionId/>
                        <RealUser/>
                        <RealUserPwd/>
                        <SSOTransferToken/>
                    </Security>
                </RequestHeader>
            </Header>
            <Body>
                <executeFinacleScriptRequest>
                    <ExecuteFinacleScriptInputVO>
                        <requestId>mobi_loan_coll.scr</requestId>
                    </ExecuteFinacleScriptInputVO>
                    <executeFinacleScript_CustomData>
                        <LOAN_ID>{$loanId}</LOAN_ID>
                        <PRIN_AMT>{$principalAmount}</PRIN_AMT>
                        <NORM_INT>{$normalInterest}</NORM_INT>
                        <PEN_INT>{$penalInterest}</PEN_INT>
                        <INT_INCOME>{$interestIncome}</INT_INCOME>
                        <NAME>{$name}</NAME>
                        <PHONE_NUMBER>{$phoneNumber}</PHONE_NUMBER>
                        <COLL_DATE>{$coldate}</COLL_DATE>
                        <TRAN_REF_NUM>{$transactionReference}</TRAN_REF_NUM>
                    </executeFinacleScript_CustomData>
                </executeFinacleScriptRequest>
            </Body>
        </FIXML>
        XML;

        try {
            // Send the request
            $response = Http::withOptions([
                'verify' => false,
            ])
                ->withHeaders([
                    'Content-Type' => 'text/xml',
                ])
                ->withBody($xmlRequest, 'text/xml')
                ->post($url);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'response' => $response->body(),
                ];
            } else {
                Log::error('Finacle API request failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => 'API request failed with status: ' . $response->status(),
                    'response' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Finacle API exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function executeNormalInterestBooking(float $amount, $systemTime, string $transactionReference): array
    {
        $url = env('ABC_API_URL');
        $bookDate = Carbon::now()->format('Y-m-d');
        // Prepare the XML request
        $xmlRequest = <<<XML
        <?xml version="1.0"?>
        <FIXML xsi:schemaLocation="http://www.finacle.com/fixml executeFinacleScript.xsd"
            xmlns="http://www.finacle.com/fixml"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Header>
                <RequestHeader>
                    <MessageKey>
                        <RequestUUID>{$transactionReference}</RequestUUID>
                        <ServiceRequestId>executeFinacleScript</ServiceRequestId>
                        <ServiceRequestVersion>10.2</ServiceRequestVersion>
                        <ChannelId>COR</ChannelId>
                        <LanguageId/>
                    </MessageKey>
                    <RequestMessageInfo>
                        <BankId>01</BankId>
                        <TimeZone/>
                        <EntityId/>
                        <EntityType/>
                        <ArmCorrelationId/>
                        <MessageDateTime>{$systemTime}</MessageDateTime>
                    </RequestMessageInfo>
                    <Security>
                        <Token>
                            <PasswordToken>
                                <UserId/>
                                <Password/>
                            </PasswordToken>
                        </Token>
                        <FICertToken/>
                        <RealUserLoginSessionId/>
                        <RealUser/>
                        <RealUserPwd/>
                        <SSOTransferToken/>
                    </Security>
                </RequestHeader>
            </Header>
            <Body>
                <executeFinacleScriptRequest>
                    <ExecuteFinacleScriptInputVO>
                        <requestId>mobi_loan_int_book.scr</requestId>
                    </ExecuteFinacleScriptInputVO>
                    <executeFinacleScript_CustomData>
                        <INT_RVB>{$amount}</INT_RVB>
                        <BOOK_DATE>{$bookDate}</BOOK_DATE>
                    </executeFinacleScript_CustomData>
                </executeFinacleScriptRequest>
            </Body>
        </FIXML>
        XML;

        try {
            // Send the request
            $response = Http::withOptions([
                'verify' => false,
            ])
                ->withHeaders([
                    'Content-Type' => 'text/xml',
                ])
                ->withBody($xmlRequest, 'text/xml')
                ->post($url);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'response' => $response->body(),
                ];
            } else {
                Log::error('Finacle API request failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => 'API request failed with status: ' . $response->status(),
                    'response' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Finacle API exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function executePenalInterestBooking(float $amount, $systemTime, string $transactionReference): array
    {
        $url = env('ABC_API_URL');
        $bookDate = Carbon::now()->format('Y-m-d');
        // Prepare the XML request
        $xmlRequest = <<<XML
        <?xml version="1.0"?>
        <FIXML xsi:schemaLocation="http://www.finacle.com/fixml executeFinacleScript.xsd"
            xmlns="http://www.finacle.com/fixml"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Header>
                <RequestHeader>
                    <MessageKey>
                        <RequestUUID>{$transactionReference}</RequestUUID>
                        <ServiceRequestId>executeFinacleScript</ServiceRequestId>
                        <ServiceRequestVersion>10.2</ServiceRequestVersion>
                        <ChannelId>COR</ChannelId>
                        <LanguageId/>
                    </MessageKey>
                    <RequestMessageInfo>
                        <BankId>01</BankId>
                        <TimeZone/>
                        <EntityId/>
                        <EntityType/>
                        <ArmCorrelationId/>
                        <MessageDateTime>{$systemTime}</MessageDateTime>
                    </RequestMessageInfo>
                    <Security>
                        <Token>
                            <PasswordToken>
                                <UserId/>
                                <Password/>
                            </PasswordToken>
                        </Token>
                        <FICertToken/>
                        <RealUserLoginSessionId/>
                        <RealUser/>
                        <RealUserPwd/>
                        <SSOTransferToken/>
                    </Security>
                </RequestHeader>
            </Header>
            <Body>
                <executeFinacleScriptRequest>
                    <ExecuteFinacleScriptInputVO>
                        <requestId>mobi_loan_pen_int_book.scr</requestId>
                    </ExecuteFinacleScriptInputVO>
                    <executeFinacleScript_CustomData>
                        <PEN_INT_RVB>{$amount}</PEN_INT_RVB>
                        <BOOK_DATE>{$bookDate}</BOOK_DATE>
                    </executeFinacleScript_CustomData>
                </executeFinacleScriptRequest>
            </Body>
        </FIXML>
        XML;

        try {
            // Send the request
            $response = Http::withOptions([
                'verify' => false,
            ])
                ->withHeaders([
                    'Content-Type' => 'text/xml',
                ])
                ->withBody($xmlRequest, 'text/xml')
                ->post($url);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'response' => $response->body(),
                ];
            } else {
                Log::error('Finacle API request failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => 'API request failed with status: ' . $response->status(),
                    'response' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Finacle API exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
