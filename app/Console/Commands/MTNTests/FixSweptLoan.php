<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Enums\LoanAccountType;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixSweptLoan extends Command
{
    protected $signature = 'mtn:fix-sweep {phone} {--txn=} {--amount=}';
    protected $description = 'Fix MTN auto sweep';

    public function handle(): int
    {
        $details = [
            'fromfri' => 'FRI:' . $this->argument('phone') . '/MSISDN',
            'tofri' => 'FRI:WKD-PERSONAL/USER',
            'amount' => [
                'amount' => $this->option('amount'),
                'currency' => 'UGX'
            ],
            'transactionid' => $this->option('txn'),
        ];

        $customer = Customer::query()->firstWhere('Telephone_Number', $this->argument('phone'));
        $loan = $customer->activeLoans()->latest()->first();

        if (empty($loan)) {
            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="ERROR_RESPONSE">' . PHP_EOL .
                '  <arguments name="reason" value="LOAN_APPLICATION_NOT_FOUND"/>' . PHP_EOL .
                '</ns0:errorResponse>';

            $this->error($content);

            return 1;
        }

        $balance = $loan->totalOutstandingBalance();

        if ($balance < data_get($details, 'amount.amount')) {
            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="AUTHORIZATION_MAX_TRANSFER_AMOUNT">' . PHP_EOL .
                '  <arguments name="reason" value="Amount is greater than the outstanding balance"/>' . PHP_EOL .
                '</ns0:errorResponse>';

            $this->error($content);

            return 1;
        }

        DB::beginTransaction();

        try {
            $transactionRecord = [
                'Partner_ID' => $loan->Partner_ID,
                'Type' => Transaction::REPAYMENT,
                'Amount' => data_get($details, 'amount.amount', 0),
                'Status' => 'Pending',
                'Telephone_Number' => $loan->customer->Telephone_Number,
                'TXN_ID' => Transaction::generateID()->toString(),
                'Loan_ID' => $loan->id,
                'Loan_Application_ID' => $loan->Loan_Application_ID,
                'Provider_TXN_ID' => data_get($details, 'transactionid'),
                'Payment_Reference' => data_get($details, 'transactionid'),
            ];

            $transaction = new Transaction($transactionRecord);
            $transaction->save();

            // todo: Process repayment
            app(ProcessLoanRepaymentAction::class)->execute($transaction);

            $transaction->Status = 'Completed';
            $transaction->save();

            // Unlink for cleared loans
            $loan = $transaction->loan->fresh();

            if ($loan->Credit_Account_Status === LoanAccountType::PaidOff->value) {
                $api = (new PaymentServiceManager($transaction))->paymentService;

                if (! $api->unlinkLoan($transaction)) {
                    $this->error('Failed to close loan: ' . $loan->id);
                }
            }

            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<bac:bankdebitcompletedresponse xmlns:bac="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '  <externaltransactionid>' . $transaction->TXN_ID . '</externaltransactionid>' . PHP_EOL .
                '  <status>SUCCESSFUL</status>' . PHP_EOL .
                '</bac:bankdebitcompletedresponse>';

            $this->info($content);

            DB::commit();

            return 0;
        } catch (\Exception $e) {
            DB::rollBack();

            $this->error('BDC Error: ' . $e->getMessage());

            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:<ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="ERROR_RESPONSE">' . PHP_EOL .
                '  <arguments name="reason" value="Error processing transaction"/>' . PHP_EOL .
                '</bac:errorResponse>';

            $this->error($content);

            return 1;
        }
    }
}
