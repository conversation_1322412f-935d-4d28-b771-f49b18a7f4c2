<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\Loans\CreateApprovedLoanAction;
use App\Models\Customer;
use App\Models\Transaction;
use App\Services\MtnApiService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class CreateLoan extends Command
{
    protected $signature = 'mtn:create-loan {transactionId}';
    protected $description = 'Complete loan application';

    /**
     */
    public function handle(): int
    {
        $transaction = Transaction::query()->firstWhere('txn_id', $this->argument('transactionId'));

        if (empty($transaction)) {
            $this->error('Transaction not found');
            return 1;
        }

        app(CreateApprovedLoanAction::class)->execute($transaction);

        $transaction->update([
            'Payment_Service_Provider' => 'MTN',
            'Status' => 'Completed'
        ]);

        return 0;
    }
}
