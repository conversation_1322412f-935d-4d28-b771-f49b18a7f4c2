# Project Conventions for Gemini

This project uses the following conventions:

## Language and Frameworks
- **PHP Version:** 8.3
- **Laravel Version:** 11.x
- **Node.js/NPM:** For frontend asset compilation

## Commands

### Installation
- **PHP Dependencies:** `composer install`
- **Node.js Dependencies:** `npm install`

### Building Assets
- `npm run build`

### Running Tests
- `php artisan test`

### Linting/Code Style
- `php artisan pint`

### Database Migrations and Seeding
- `php artisan migrate --force`
- `php artisan db:seed --force`

## Directory Structure
- Standard Laravel directory structure.

## Git
- Follows a standard Git workflow.