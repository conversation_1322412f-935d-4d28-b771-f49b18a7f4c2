<?php

namespace App\Http\Controllers;

use App\Models\AssetLocation;
use App\Models\Partner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AssetLocationController extends Controller
{
    // Display a listing of the resource
    public function index()
    {
        $assetLocations = AssetLocation::all();
        return view('asset-locations.index', compact('assetLocations'));
    }

    // Show the form for creating a new resource
    public function create()
    {
        if (Auth::user()->is_admin) {
            $partners = Partner::all();
        } else {
            $partners = Partner::where('id', Auth::user()->partner_id)->get();
        }
        return view('asset-locations.create', compact('partners'));
    }

    // Store a newly created resource in storage
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'partner_id' => 'nullable|numeric',
        ]);

        AssetLocation::create($request->all());

        return redirect()->route('asset-locations.index')
            ->with('success', 'Asset Location created successfully.');
    }

    // Display the specified resource
    public function show(AssetLocation $assetLocation)
    {
        return view('asset-locations.show', compact('assetLocation'));
    }

    // Show the form for editing the specified resource
    public function edit(AssetLocation $assetLocation)
    {
        if (Auth::user()->is_admin) {
            $partners = Partner::all();
        } else {
            $partners = Partner::where('id', Auth::user()->partner_id)->get();
        }
        return view('asset-locations.edit', compact('assetLocation', 'partners'));
    }

    // Update the specified resource in storage
    public function update(Request $request, AssetLocation $assetLocation)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'partner_id' => 'nullable|numeric',
        ]);

        $assetLocation->update($request->all());

        return redirect()->route('asset-locations.index')
            ->with('success', 'Asset Location updated successfully.');
    }

    // Remove the specified resource from storage
    public function destroy(AssetLocation $assetLocation)
    {
        $assetLocation->delete();

        return redirect()->route('asset-locations.index')
            ->with('success', 'Asset Location deleted successfully.');
    }
}
