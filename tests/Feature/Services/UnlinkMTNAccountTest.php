<?php

namespace Tests\Feature\Services;

use App\Enums\LoanAccountType;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\Transaction;
use App\Services\MtnApiService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class UnlinkMTNAccountTest extends TestCase
{
    use RefreshDatabase;

    private MtnApiService $service;
    private MockHandler $mockHandler;
    private Client $client;
    private Customer $customer;
    private Transaction $transaction;

    /**
     * @return string
     */
    public function successfulXmlResponse(): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?><ns0:unlinkfinancialresourceinformationresponse xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend"/>';
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        // Set up HTTP mocking
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);
        $this->client = new Client(['handler' => $handlerStack]);

        // Create a partial mock of the service that returns our mocked client
        $this->service = Mockery::mock(MtnApiService::class, ['test'])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        // Mock the getClient method to return our mocked client
        $this->service->shouldReceive('getClient')
            ->andReturn($this->client);

        // Set up test data
        $this->setupTestData();
    }

    private function setupTestData(): void
    {
        $this->customer = Customer::factory()->withOptions()->create();
    }

    private function getPayload(Customer $customer): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
            '    <fri>FRI:' . $customer->optional_savings_account_number . '@WKD-SAVINGS/SP</fri>' . PHP_EOL .
            '    <msisdn>' . $customer->Telephone_Number . '</msisdn>' . PHP_EOL .
            '</ns0:unlinkfinancialresourceinformationrequest>';
    }

    public function test_unlink_method_is_successful()
    {
        // Mock successful response (empty body indicates success)
        $this->mockHandler->append(new Response(200, ['Content-Type' => 'application/xml'], $this->successfulXmlResponse()));

        $this->assertTrue($this->service->unlink($this->getPayload($this->customer)));
    }

    public function test_opting_out_customer_is_successful()
    {
        $this->mockHandler->append(new Response(200, ['Content-Type' => 'application/xml'], $this->successfulXmlResponse()));
        $result = $this->service->unlinkCustomer($this->customer);

        $this->assertTrue($result);
    }

    public function test_unlinking_loan_is_successful()
    {
        $this->mockHandler->append(new Response(200, ['Content-Type' => 'application/xml'], $this->successfulXmlResponse()));
        $loan = Loan::factory()->create([
            'Customer_ID' => $this->customer->id
        ]);
        $transaction = Transaction::factory()->create([
            'Telephone_Number' => $this->customer->Telephone_Number,
            'Loan_Application_ID' => $loan->Loan_Application_ID,
            'Loan_ID' => $loan->id,
            'Amount' => $loan->Credit_Amount,
        ]);
        $options = $this->customer->options;
        data_set($options, 'loanaccounts.loanaccount.accountnumber', '**********');
        $this->customer->options = $options;
        $this->customer->save();

        $this->assertTrue($this->service->unlinkLoan($transaction));
        $this->customer->refresh();
        $this->assertNull($this->customer->loan_application_account_number);
        $this->assertNotEquals('**********', $this->customer->loan_application_account_number);
    }

    public function test_closing_loan_returns_false_for_active_loan()
    {
        $loan = Loan::factory()->create([
            'Customer_ID' => $this->customer->id,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value
        ]);
        $transaction = Transaction::factory()->create([
            'Telephone_Number' => $this->customer->Telephone_Number,
            'Loan_Application_ID' => $loan->Loan_Application_ID,
            'Loan_ID' => $loan->id,
            'Amount' => $loan->Credit_Amount,
        ]);

        $this->assertFalse($this->service->closeLoan($transaction));
    }

    public function test_closing_loan_calls_unlink_method()
    {
        $loan = Loan::factory()->create([
            'Customer_ID' => $this->customer->id,
            'Credit_Account_Status' => LoanAccountType::PaidOff->value
        ]);
        $transaction = Transaction::factory()->create([
            'Telephone_Number' => $this->customer->Telephone_Number,
            'Loan_Application_ID' => $loan->Loan_Application_ID,
            'Loan_ID' => $loan->id,
            'Amount' => $loan->Credit_Amount,
        ]);

        $this->mockHandler->append(new Response(200, ['Content-Type' => 'application/xml'], $this->successfulXmlResponse()));
        $this->service->shouldReceive('unlinkLoan')->andReturn(true);
        $this->assertTrue($this->service->closeLoan($transaction));
    }

    public function test_opting_out_customer_fails_without_savings_account()
    {
        // Create a customer without optional savings account (no options set)
        $customer = Customer::factory()->create();

        // Verify the customer doesn't have an optional savings account
        $this->assertNull($customer->optional_savings_account_number);

        // Mock log expectations for the validation error
        Log::shouldReceive('error')
            ->with(Mockery::pattern('/MTN Unlink Validation Error:/'))
            ->once();

        // The method should return false when validation fails
        $this->assertFalse($this->service->unlinkCustomer($customer));
    }

    public function test_unlink_method_handles_request_exception()
    {
        // Mock request exception
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $response = new Response(400, [], 'Bad Request');
        $exception = new RequestException('Bad Request', $request, $response);

        $this->mockHandler->append($exception);

        $this->assertFalse($this->service->unlink($this->getPayload($this->customer)));
    }

    public function test_unlink_method_handles_general_exception()
    {
        // Mock general exception
        $this->mockHandler->append(new \Exception('Network error'));

        $this->assertFalse($this->service->unlink($this->getPayload($this->customer)));
    }

    public function test_unlink_customer_returns_false_on_failure()
    {
        // Mock request exception
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $response = new Response(500, [], 'Internal Server Error');
        $exception = new RequestException('Internal Server Error', $request, $response);

        $this->mockHandler->append($exception);

        $result = $this->service->unlinkCustomer($this->customer);

        $this->assertFalse($result);
    }

    public function test_unlink_loan_does_not_remove_loan_application_number_when_unlink_fails()
    {
        $loan = Loan::factory()->create([
            'Customer_ID' => $this->customer->id
        ]);
        $transaction = Transaction::factory()->create([
            'Telephone_Number' => $this->customer->Telephone_Number,
            'Loan_Application_ID' => $loan->Loan_Application_ID,
            'Loan_ID' => $loan->id,
            'Amount' => $loan->Credit_Amount,
        ]);
        $options = $this->customer->options;
        data_set($options, 'loanaccounts.loanaccount.accountnumber', '**********');
        $this->customer->options = $options;
        $this->customer->save();


        // Mock request exception
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $response = new Response(400, [], 'Bad Request');
        $exception = new RequestException('Bad Request', $request, $response);

        $this->mockHandler->append($exception);

        $customer = $this->customer->fresh();

        $this->assertFalse($this->service->unlinkLoan($transaction));
        $this->assertNotNull($customer->loan_application_account_number);
        $this->assertEquals('**********', $customer->loan_application_account_number);
    }

    public function test_unlink_method_handles_request_exception_without_response()
    {
        // Mock request exception without response
        $request = new Request('POST', '/sp/unlinkfinancialresourceinformation');
        $exception = new RequestException('Connection timeout', $request);

        $this->mockHandler->append($exception);

        $payload = '<test>payload</test>';

        $result = $this->service->unlink($payload);

        $this->assertFalse($result);
    }
}
