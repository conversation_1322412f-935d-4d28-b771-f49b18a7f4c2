<?php

namespace Tests\Unit;

use App\Models\Partner;
use App\Models\PartnerApiSetting;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class PartnerApiSettingTest extends TestCase
{
    use DatabaseTransactions;

    public function test_it_encrypts_and_decrypts_api_key_correctly()
    {
        // Create a partner
        $partner = Partner::factory()->create();

        // Original API key
        $originalApiKey = 'test-api-key-123';

        // Create a partner API setting
        $apiSetting = PartnerApiSetting::create([
            'partner_id' => $partner->id,
            'api_name' => 'test-api',
            'api_key' => $originalApiKey,
        ]);

        // Refresh the model to ensure we're getting the value from the database
        $apiSetting->refresh();

        // The api_key attribute should return the decrypted value
        $this->assertEquals($originalApiKey, $apiSetting->api_key);

        // The raw value in the database should be encrypted
        $encryptedValue = $apiSetting->getAttributes()['api_key'];
        $this->assertNotEquals($originalApiKey, $encryptedValue);

        // The encrypted value should be decryptable
        $this->assertEquals($originalApiKey, decrypt($encryptedValue));
    }

    public function test_it_handles_spiro_api_keys_correctly()
    {
        // Create a partner
        $partner = Partner::factory()->create();

        // Original API key
        $originalApiKey = 'spiro-api-key-123';

        // Create a partner API setting for spiro
        $apiSetting = PartnerApiSetting::create([
            'partner_id' => $partner->id,
            'api_name' => 'spiro',
            'api_key' => $originalApiKey,
        ]);

        // Refresh the model to ensure we're getting the value from the database
        $apiSetting->refresh();

        // For spiro, the api_key should be stored as-is
        $this->assertEquals($originalApiKey, $apiSetting->api_key);
        $this->assertEquals($originalApiKey, $apiSetting->getAttributes()['api_key']);
    }
}
