<?php

namespace Database\Factories;

use App\Models\LoanProduct;
use App\Models\LoanProductType;
use App\Models\Partner;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoanProduct>
 */
class LoanProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LoanProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'Partner_ID' => Partner::factory(),
            'code' => 'LP-' . fake()->unique()->ean8(),
            'name' => fake()->words(3, true) . ' Loan',
            'loan_product_type_id' => (LoanProductType::create([
                'name' => 'Mobile Loan',
                'code' => '1001',
            ]))->id,
            'minimum_principal_amount' => 10000,
            'default_principal_amount' => 50000,
            'maximum_principal_amount' => 500000,
            'auto_debit' => 'No',
            'decimal_place' => 0,
            'round_up_or_off_all_interest' => 1,
            'repayment_order' => '["Penalty","Interest","Principal","Fees"]',
            'arrears_auto_write_off_days' => 180,

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
