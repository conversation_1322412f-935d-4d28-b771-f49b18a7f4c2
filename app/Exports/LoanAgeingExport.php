<?php

namespace App\Exports;

use App\Actions\Reports\GetLoanAgeingReportDetailsAction;
use App\Models\LoanLossProvision;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;

class LoanAgeingExport implements FromView, WithTitle
{
    public function __construct(protected array $filters)
    {
    }

    public function view(): \Illuminate\Contracts\View\View
    {
        return view('excel.loan-ageing', [
            'records' => app(GetLoanAgeingReportDetailsAction::class)
                ->filters($this->filters)
                ->execute(),
            'ageingDays' => LoanLossProvision::query()
                ->select('id', 'minimum_days', 'maximum_days')
                ->orderBy('minimum_days')->get(),
            'partnerName' => auth()->user()?->partner->Institution_Name,
            'filters' => $this->filters,
        ]);
    }

    public function title(): string
    {
        return 'Loan Ageing';
    }
}
