<table>
    <thead>
        <tr>
            <th colspan="8" style="font-size: 18px; font-weight: bold; text-align: center;">{{ $partnerName }}</th>
        </tr>
        <tr>
            <th colspan="8" style="font-size: 14px; font-weight: bold; text-align: center;">General <PERSON><PERSON> Breakdown
            </th>
        </tr>
        <tr>
            <th colspan="8" style="font-size: 10px; text-align: center;">Period: {{ $filters['startDate'] }} to
                {{ $filters['endDate'] }}</th>
        </tr>
        <tr class="table-header">
            <th style="font-weight: bold; text-align: left; width: 150px;">ID#</th>
            <th style="font-weight: bold; text-align: left; width: 200px;">Account</th>
            <th style="font-weight: bold; text-align: left; width: 100px;">Customer Name</th>
            <th style="font-weight: bold; text-align: left; width: 100px;">Telephone Number</th>
            <th style="font-weight: bold; text-align: right; width: 130px;">Entry Date</th>
            <th style="font-weight: bold; text-align: right; width: 100px;">DR</th>
            <th style="font-weight: bold; text-align: right; width: 100px;">CR</th>
            <th style="font-weight: bold; text-align: right; width: 100px;">Balance</th>
        </tr>
    </thead>
    <tbody>
        @forelse ($records as $record)
            <tr>
                <td>{{ $record->txn_id }}</td>
                <td>{{ $record->account_name }}</td>
                <td>{{ $record->customer?->name }}</td>
                <td>{{ $record->customer?->Telephone_Number }}</td>
                <td style="text-align: right">{{ $record->created_at }}</td>
                <td style="text-align: right">{{ $record->debit_amount }}</td>
                <td style="text-align: right">{{ $record->credit_amount }}</td>
                <td style="text-align: right">{{ $record->current_balance }}</td>
            </tr>
        @empty
            <tr>
                <td colspan="7">No records found</td>
            </tr>
        @endforelse
    </tbody>
</table>
<x-print-footer />
