<?php

namespace App\Listeners;

use App\Models\Partner;
use App\Models\Transaction;
use App\Mail\AssetDisbursed;
use App\Events\LoanDisbursed;
use Illuminate\Support\Facades\Mail;

class SendLoanDisbursedMail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(LoanDisbursed $event): void
    {
        $partner = $event->loan->partner;
        $transaction = Transaction::query()->firstWhere('Loan_ID', $event->loan->id);

        $assetPartner = Partner::query()->firstWhere('Institution_Name', 'like', '%' . $transaction?->Asset_Provider . '%');

        if ($partner?->Email_Notification_Recipients) {
            $recipients = array_map('trim', explode(',', $partner->Email_Notification_Recipients));
            $mail = Mail::to($recipients);

            if (! empty($assetPartner->Email_Notification_Recipients)) {
                $recipients = array_map('trim', explode(',', $assetPartner->Email_Notification_Recipients));
                $mail->cc($recipients);
            }

            $mail->send(new \App\Mail\LoanDisbursed($event->loan));

            if ($event->loan->asset) {
                $mail->send(new AssetDisbursed($event->loan));
            }

            return;
        }
    }
}
