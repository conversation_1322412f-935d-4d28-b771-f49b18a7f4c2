@extends('layouts/contentNavbarLayout')
@section('title', 'Cash Sale - Create')
@section('content')

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h3>Edit Cash Sale</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('cash-sales.update', $cashSale) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="customer_name" class="form-label">Customer Name</label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name"
                                value="{{ old('customer_name', $cashSale->customer_name) }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="customer_phone" class="form-label">Customer Phone</label>
                            <input type="text" class="form-control" id="customer_phone" name="customer_phone"
                                value="{{ old('customer_phone', $cashSale->customer_phone) }}" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="customer_location" class="form-label">Customer Location</label>
                            <input type="text" class="form-control" id="customer_location" name="customer_location"
                                value="{{ old('customer_location', $cashSale->customer_location) }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="amount" class="form-label">Amount</label>
                            <input type="number" step="0.01" class="form-control" id="amount" name="amount"
                                value="{{ old('amount', $cashSale->amount) }}" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="vin_no" class="form-label">VIN No.</label>
                            <input type="text" class="form-control" id="vin_no" name="vin_no"
                                value="{{ old('vin_no', $cashSale->vin_no) }}">
                        </div>
                        <div class="col-md-6">
                            <label for="reg_no" class="form-label">Reg No.</label>
                            <input type="text" class="form-control" id="reg_no" name="reg_no"
                                value="{{ old('reg_no', $cashSale->reg_no) }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="tin" class="form-label">TIN No.</label>
                            <input type="text" class="form-control" id="tin" name="tin"
                                value="{{ old('tin', $cashSale->tin) }}">
                        </div>
                        <div class="col-md-6">
                            <label for="usage" class="form-label">Usage</label>
                            <input type="text" class="form-control" id="usage" name="usage"
                                value="{{ old('usage', $cashSale->usage) }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="sales_executive" class="form-label">Sales Executive</label>
                            <input type="text" class="form-control" id="sales_executive" name="sales_executive"
                                value="{{ old('sales_executive', $cashSale->sales_executive) }}">
                        </div>
                        <div class="col-md-6">
                            <label for="lead_source" class="form-label">Lead Source</label>
                            <input type="text" class="form-control" id="lead_source" name="lead_source"
                                value="{{ old('lead_source', $cashSale->lead_source) }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="financier" class="form-label">Financier</label>
                            <input type="text" class="form-control" id="financier" name="financier"
                                value="{{ old('financier', $cashSale->financier) }}">
                        </div>
                        <div class="col-md-6">
                            <label for="receipt" class="form-label">Current Receipt</label>
                            @if ($cashSale->receipt_path)
                                <div class="mb-2">
                                    @if (pathinfo($cashSale->receipt_path, PATHINFO_EXTENSION) === 'pdf')
                                        <a href="{{ Storage::url($cashSale->receipt_path) }}" target="_blank"
                                            class="btn btn-sm btn-info">View Current PDF</a>
                                    @else
                                        <img src="{{ Storage::url($cashSale->receipt_path) }}" alt="Current Receipt"
                                            class="img-thumbnail" style="max-height: 150px;">
                                    @endif
                                </div>
                            @else
                                <p class="text-muted">No receipt uploaded</p>
                            @endif

                            <label for="receipt" class="form-label">Upload New Receipt (Leave blank to keep
                                current)</label>
                            <input type="file" class="form-control" id="receipt" name="receipt"
                                accept="image/*,.pdf">
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">Update Record</button>
                        <a href="{{ route('cash-sales.index') }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
