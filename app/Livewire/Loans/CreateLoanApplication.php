<?php

namespace App\Livewire\Loans;

use App\Models\Accounts\Account;
use App\Models\LoanProduct;
use App\Models\LoanProductType;
use App\Services\Account\AccountSeederService;
use Livewire\Component;

class CreateLoanApplication extends Component
{
    public ?int $loanProductId = null;
    public ?int $amountRequested = null;
    public string $rangeHelpText = '';
    public string $creditApplicationDate = '';
    public LoanProduct $loanProduct;

    public function mount()
    {
        $this->loanProduct = new LoanProduct();
        $this->creditApplicationDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.loans.loan-applications.create', $this->getViewData());
    }

    public function updatedLoanProductId($value)
    {
        $loanProduct = LoanProduct::query()->select('Minimum_Principal_Amount', 'Default_Principal_Amount', 'Maximum_Principal_Amount')->find($value);

        if (!$this->loanProduct) {
            return;
        }

        $this->loanProduct->fill($loanProduct->toArray());

        $this->amountRequested = $this->loanProduct->Default_Principal_Amount;
        $this->rangeHelpText = 'Min: ' . number_format($this->loanProduct->Minimum_Principal_Amount) . ', Max: ' . number_format($this->loanProduct->Maximum_Principal_Amount);
    }

    public function createApplication()
    {
        $this->validate([
            'loanProductId' => ['required', 'exists:loan_products,id'],
            'creditApplicationDate' => ['required', 'date', 'after_or_equal:' . $this->creditApplicationDate],
        ]);

        // dd('here');
    }

    protected function getViewData()
    {
        $loan_products = LoanProduct::query()->pluck('Name', 'id');

        return [
            'loan_products' => $loan_products,
        ];
    }
}
