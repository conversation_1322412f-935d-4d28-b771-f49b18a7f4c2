<?php

namespace Tests\Feature\Api;

use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Enums\LoanAccountType;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Carbon\Carbon;
use Tests\TestCase;
use App\Models\Customer;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\DB;

class MtnAgentControllerTest extends TestCase
{
    use DatabaseTransactions, WithFaker;

    public function setUp(): void
    {
        parent::setUp();
        Queue::fake();
    }

    // @REGISTER_CUSTOMER
    public function test_it_can_register_a_new_customer()
    {
        $first_name = $this->faker->firstName();
        $last_name = $this->faker->lastName();
        $dob = $this->faker->date();

        $xmlPayload =
            '<?xml version="1.0" encoding="UTF-8"?>'
            . '<ns0:customerregistrationrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">'
            . '<resource>FRI:**********67/MSISDN</resource>'
            . '<name>'
                . '<firstname>'. $first_name .'</firstname>'
                . '<lastname>'. $last_name .'</lastname>'
            . '</name>'
            . '<gender>Male</gender>'
            . '<dob>'. $dob. '</dob>'
            . '<idtype>NRIN</idtype>'
            . '<idnumber>NATIONAL_ID_NUMBER</idnumber>'
            . '<idexpiry>2024-11-30+03:00</idexpiry>'
            . '<addresses/>'
            . '</ns0:customerregistrationrequest>';

        $response = $this->call(
            'POST',
            '/api/v1/customerregistration',
            [], // Parameters
            [], // Cookies
            [], // Files
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        $response->assertStatus(200);

        $decoded = html_entity_decode($response->getContent());
        $this->assertStringContainsString('<status>PENDING</status>', $decoded);

        $last_customer = Customer::where('Telephone_Number', '**********67')->first();
        $this->assertEquals($first_name, $last_customer->First_Name);
        $this->assertEquals($last_name, $last_customer->Last_Name);
    }

    public function test_it_does_not_create_customer_when_there_are_validation_errors(){
        $first_name = $this->faker->firstName();
        $last_name = $this->faker->lastName();
        $dob = $this->faker->date();

        $xmlPayload =
            '<?xml version="1.0" encoding="UTF-8"?>'
            . '<ns0:customerregistrationrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">'
            . '<resource>FRI:**********67/MSISDN</resource>'
            . '<name>'
                . '<firstname>'. $first_name .'</firstname>'
                . '<lastname>'. $last_name .'</lastname>'
            . '</name>'
            . '<dob>'. $dob. '</dob>'
            . '<idtype>NRIN</idtype>'
            . '<idnumber>NATIONAL_ID_NUMBER</idnumber>'
            . '<idexpiry>2024-11-30+03:00</idexpiry>'
            . '<addresses/>'
            . '</ns0:customerregistrationrequest>';

        $response = $this->call(
            'POST',
            '/api/v1/customerregistration',
            [], // Parameters
            [], // Cookies
            [], // Files
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        $decoded = html_entity_decode($response->getContent());

        $this->assertStringContainsString('<errorcode>ACCOUNT_NOT_CREATED</errorcode>', $decoded);

        $this->assertNull(Customer::where('Telephone_Number', '**********67')->first());
    }

    public function test_returns_unregistered_response_when_customer_not_found()
    {
        // Arrange
        $xmlContent = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:************/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlContent
        );

        $response->assertStatus(200);
        $content = $response->content();

        // Assert
        $this->assertStringContainsString('<status>UNREGISTERED</status>', $content);
        $this->assertStringContainsString('<savingsaccounts/>', $content);
        $this->assertStringContainsString('<loanaccounts/>', $content);
    }

    /**
     * Customer exists in the system but has not opted in.
     *
     * @return void
     */
    public function test_returns_unregistered_response_when_customer_has_no_optional_savings_account()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
            'options' => []
        ]);

        $payload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:' . $customer->Telephone_Number . '/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $payload
        );
        $content = $response->content();

        // Assert
        $response->assertStatus(200);
        $this->assertStringContainsString('<status>UNREGISTERED</status>', $content);
        $this->assertStringContainsString('<savingsaccounts/>', $content);
        $this->assertStringContainsString('<loanaccounts/>', $content);
    }

    public function test_returns_registered_response_without_active_loans()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
            'options' => [
                'savingsaccount' => [
                    'accountnumber' => '***********',
                    'status' => 'ACTIVE',
                    'balance' => [
                        'amount' => 5000,
                        'currency' => 'UGX'
                    ],
                    'savingsaccounttype' => 'SAVINGS'
                ]
            ]
        ]);

        $payload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:' . $customer->Telephone_Number . '/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $payload
        );
        $content = $response->content();

        // Assert
        $response->assertStatus(200);
        $this->assertStringContainsString('<status>REGISTERED</status>', $content);
        $this->assertStringContainsString('<accountnumber>***********</accountnumber>', $content);
    }

    public function test_returns_registered_response_with_active_loan_details()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
            'options' => [
                'savingsaccount' => [
                    'accountnumber' => '***********',
                    'status' => 'ACTIVE',
                    'balance' => [
                        'amount' => 5000,
                        'currency' => 'UGX'
                    ],
                    'savingsaccounttype' => 'SAVINGS'
                ],
                'loanaccounts' => [
                    'loanaccount' => [
                        'accountnumber' => 'L***********'
                    ]
                ]
            ]
        ]);

        $loan = Loan::factory()->createQuietly([
            'Customer_ID' => $customer->id,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value,
            'Credit_Amount' => 100000
        ]);

        // Mock the totalOutstandingBalance method
        $this->partialMock(Loan::class, function ($mock) {
            $mock->shouldReceive('totalOutstandingBalance')->andReturn(100000);
        });

        $payload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:getcustomerdetailsrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <resource>FRI:' . $customer->Telephone_Number . '/MSISDN</resource>
            </ns0:getcustomerdetailsrequest>';

        $response = $this->call('POST',
            '/api/v1/customerdetails',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $payload
        );
        $content = $response->content();

        // Assert
        $this->assertStringContainsString('<status>REGISTERED</status>', $content);
        $this->assertStringContainsString('<accountnumber>***********</accountnumber>', $content);
        $this->assertStringContainsString('<duedate>' . $loan->Maturity_Date->toDateString() . '</duedate>', $content);
    }

    // @BANK_DEBIT_COMPLETED
    public function test_bank_debit_completed_returns_error_when_customer_has_no_active_loans()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
        ]);

        $xmlPayload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:bankdebitcompletedrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <fromfri>FRI:' . $customer->Telephone_Number . '/MSISDN</fromfri>
                <amount>
                    <amount>50000</amount>
                    <currency>UGX</currency>
                </amount>
                <transactionid>TXN123456789</transactionid>
            </ns0:bankdebitcompletedrequest>';

        // Act
        $response = $this->call('POST',
            '/api/v1/bankdebitcompleted',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        // Assert
        $response->assertStatus(200); // Laravel wraps the response
        $content = $response->content();
        $this->assertStringContainsString('500 Internal Server Error', $content);
        $this->assertStringContainsString('LOAN_APPLICATION_NOT_FOUND', $content);
        $this->assertStringContainsString('Loan account not found', $content);
    }

    public function test_bank_debit_completed_returns_error_when_swept_amount_exceeds_outstanding_balance()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
        ]);

        $loanApplication = LoanApplication::factory()->create([
            'Customer_ID' => $customer->id,
        ]);

        $loan = Loan::factory()->createQuietly([
            'Customer_ID' => $customer->id,
            'Loan_Application_ID' => $loanApplication->id,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value,
            'Credit_Amount' => 100000
        ]);

        // Mock the totalOutstandingBalance method to return a smaller amount
        $this->partialMock(Loan::class, function ($mock) {
            $mock->shouldReceive('totalOutstandingBalance')->andReturn(30000);
        });

        $xmlPayload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:bankdebitcompletedrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <fromfri>FRI:' . $customer->Telephone_Number . '/MSISDN</fromfri>
                <amount>
                    <amount>50000</amount>
                    <currency>UGX</currency>
                </amount>
                <transactionid>TXN123456789</transactionid>
            </ns0:bankdebitcompletedrequest>';

        // Act
        $response = $this->call('POST',
            '/api/v1/bankdebitcompleted',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        // Assert
        $response->assertStatus(200); // Laravel wraps the response
        $content = $response->content();
        $this->assertStringContainsString('500 Internal Server Error', $content);
        $this->assertStringContainsString('AUTHORIZATION_MAX_TRANSFER_AMOUNT', $content);
        $this->assertStringContainsString('Amount is greater than the outstanding balance', $content);
    }

    public function test_bank_debit_completed_processes_successful_repayment()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
        ]);

        $loanApplication = LoanApplication::factory()->create([
            'Customer_ID' => $customer->id,
        ]);

        $loan = Loan::factory()->createQuietly([
            'Customer_ID' => $customer->id,
            'Loan_Application_ID' => $loanApplication->id,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value,
            'Credit_Amount' => 100000,
            'Partner_ID' => $loanApplication->Partner_ID,
        ]);

        // Mock the totalOutstandingBalance method
        $this->partialMock(Loan::class, function ($mock) {
            $mock->shouldReceive('totalOutstandingBalance')->andReturn(50000);
        });

        // Mock ProcessLoanRepaymentAction
        $this->mock(ProcessLoanRepaymentAction::class, function ($mock) {
            $mock->shouldReceive('execute')->once()->andReturn(true);
        });

        $xmlPayload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:bankdebitcompletedrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <fromfri>FRI:' . $customer->Telephone_Number . '/MSISDN</fromfri>
                <amount>
                    <amount>30000</amount>
                    <currency>UGX</currency>
                </amount>
                <transactionid>TXN123456789</transactionid>
            </ns0:bankdebitcompletedrequest>';

        // Act
        $response = $this->call('POST',
            '/api/v1/bankdebitcompleted',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        // Assert
        $response->assertStatus(200);
        $content = $response->content();
        dump('Successful test content: ' . $content);
        $this->assertStringContainsString('<status>SUCCESSFUL</status>', $content);
        $this->assertStringContainsString('<externaltransactionid>', $content);

        // Verify transaction was created
        $this->assertDatabaseHas('transactions', [
            'Type' => Transaction::REPAYMENT,
            'Amount' => 30000,
            'Status' => 'Completed',
            'Telephone_Number' => $customer->Telephone_Number,
            'Loan_ID' => $loan->id,
            'Provider_TXN_ID' => 'TXN123456789',
            'Payment_Reference' => 'TXN123456789'
        ]);
    }

    public function test_bank_debit_completed_handles_paid_off_loan_and_calls_close_loan()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
        ]);

        $loanApplication = LoanApplication::factory()->create([
            'Customer_ID' => $customer->id,
        ]);

        $loan = Loan::factory()->createQuietly([
            'Customer_ID' => $customer->id,
            'Loan_Application_ID' => $loanApplication->id,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value,
            'Credit_Amount' => 100000,
            'Partner_ID' => $loanApplication->Partner_ID,
        ]);

        // Mock the totalOutstandingBalance method
        $this->partialMock(Loan::class, function ($mock) {
            $mock->shouldReceive('totalOutstandingBalance')->andReturn(30000);
        });

        // Mock ProcessLoanRepaymentAction
        $this->mock(ProcessLoanRepaymentAction::class, function ($mock) {
            $mock->shouldReceive('execute')->once()->andReturn(true);
        });

        // Mock PaymentServiceManager and its paymentService
        $mockPaymentService = $this->createMock(\App\Services\Contracts\ProvidesTransactableAPIs::class);
        $mockPaymentService->expects($this->once())
            ->method('closeLoan')
            ->willReturn(true);

        $this->mock(PaymentServiceManager::class, function ($mock) use ($mockPaymentService) {
            $mock->paymentService = $mockPaymentService;
        });

        $xmlPayload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:bankdebitcompletedrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <fromfri>FRI:' . $customer->Telephone_Number . '/MSISDN</fromfri>
                <amount>
                    <amount>30000</amount>
                    <currency>UGX</currency>
                </amount>
                <transactionid>TXN123456790</transactionid>
            </ns0:bankdebitcompletedrequest>';

        // Act
        $response = $this->call('POST',
            '/api/v1/bankdebitcompleted',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        // We need to manually update the loan status to PaidOff to simulate the repayment processing
        $transaction = Transaction::where('Provider_TXN_ID', 'TXN123456790')->first();
        $loan->update(['Credit_Account_Status' => LoanAccountType::PaidOff->value]);
        $transaction->loan->refresh();

        // Assert
        $response->assertStatus(200);
        $content = $response->content();
        $this->assertStringContainsString('<status>SUCCESSFUL</status>', $content);
    }

    public function test_bank_debit_completed_handles_exception_during_processing()
    {
        // Arrange
        $customer = Customer::factory()->create([
            'Telephone_Number' => '************',
        ]);

        $loanApplication = LoanApplication::factory()->create([
            'Customer_ID' => $customer->id,
        ]);

        $loan = Loan::factory()->createQuietly([
            'Customer_ID' => $customer->id,
            'Loan_Application_ID' => $loanApplication->id,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value,
            'Credit_Amount' => 100000,
            'Partner_ID' => $loanApplication->Partner_ID,
        ]);

        // Mock the totalOutstandingBalance method
        $this->partialMock(Loan::class, function ($mock) {
            $mock->shouldReceive('totalOutstandingBalance')->andReturn(50000);
        });

        // Mock ProcessLoanRepaymentAction to throw an exception
        $this->mock(ProcessLoanRepaymentAction::class, function ($mock) {
            $mock->shouldReceive('execute')->once()->andThrow(new \Exception('Processing failed'));
        });

        $xmlPayload = '<?xml version="1.0" encoding="UTF-8"?>
            <ns0:bankdebitcompletedrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">
                <fromfri>FRI:' . $customer->Telephone_Number . '/MSISDN</fromfri>
                <amount>
                    <amount>30000</amount>
                    <currency>UGX</currency>
                </amount>
                <transactionid>TXN123456791</transactionid>
            </ns0:bankdebitcompletedrequest>';

        // Act
        $response = $this->call('POST',
            '/api/v1/bankdebitcompleted',
            [], [], [],
            [
                'CONTENT_TYPE' => 'application/xml',
                'HTTP_ACCEPT' => 'application/xml',
            ],
            $xmlPayload
        );

        // Assert
        $response->assertStatus(200); // Laravel wraps the response
        $content = $response->content();
        $this->assertStringContainsString('500 Internal Server Error', $content);
        $this->assertStringContainsString('ERROR_RESPONSE', $content);
        $this->assertStringContainsString('Error processing transaction', $content);

        // Verify transaction was created but status remains Pending due to rollback
        $this->assertDatabaseMissing('transactions', [
            'Provider_TXN_ID' => 'TXN123456791',
            'Status' => 'Completed'
        ]);
    }

}
