<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\CreateTestTransactionAction;
use App\Models\Transaction;
use App\Services\MockMtnApiService;
use App\Exceptions\MtnApiException;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class Collect extends Command
{
    protected $signature = 'mtn:collect {reference}';
    protected $description = 'Test MTN collect money from agent API';

    public function handle(): int
    {
        try {
            $transaction = Transaction::query()->firstWhere('txn_id', $this->argument('reference'));

            $mtnService = (new PaymentServiceManager($transaction))->paymentService;
            $response = $mtnService->collect($transaction->Telephone_Number, $transaction->Amount, $transaction->TXN_ID, $transaction->Type);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());

            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }

            return 1;
        }
    }

    /**
     * Ask for input with validation
     *
     * @param string $question
     * @param string $field
     * @param array $rules
     * @param string|null $errorMessage
     * @return string
     */
    private function askValid(string $question, string $field, array $rules, ?string $errorMessage = null): string
    {
        do {
            $value = $this->ask($question);
            $validator = validator([$field => $value], [$field => $rules]);

            if ($validator->fails()) {
                $this->error($errorMessage ?? $validator->errors()->first());
                continue;
            }

            return $value;
        } while (true);
    }
}
