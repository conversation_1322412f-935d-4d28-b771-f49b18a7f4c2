<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetWithdrawalsReportDetailsAction;
use Livewire\Component;

class WithdrawalReport extends Component
{
    public string $startDate = '';
    public string $endDate = '';

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.withdrawal-report', [
            'withdrawals' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return redirect()->route('export.withdrawals.pdf', ['startDate' => $this->startDate, 'endDate' => $this->endDate]);
    }

    public function excelExport(): true
    {
        // todo: generate excel
        session()->flash('error', 'Exporting to excel is not yet supported for this report.');

        return true;
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetWithdrawalsReportDetailsAction::class)
            ->paginate()
            ->filters([
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
            ])
            ->execute();
    }
}
