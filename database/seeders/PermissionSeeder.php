<?php

namespace Database\Seeders;

use App\Helpers\SystemResource;
use App\Helpers\Operations;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        // Permission::truncate();
        // DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $system_resource = SystemResource::getSystemResources();
        $supported_operations = Operations::getSupportedOperations();
        foreach ($system_resource as $resource) {
            foreach ($supported_operations as $operation) {
                Permission::updateOrCreate([
                    'name' => $operation . ' ' . $resource
                ]);
            }
        }
    }
}
