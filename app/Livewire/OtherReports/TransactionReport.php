<?php

namespace App\Livewire\OtherReports;

use App\Actions\OtherReports\GetTransactionsReportDetailsAction;
use App\Actions\Reports\GetFullPaymentReportDetailsAction;
use App\Exports\TransactionsExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class TransactionReport extends Component
{
    use ExportsData;

    public string $transactionStatus = '';
    public bool $hasSavingsPreference = false;

    public function mount(): void
    {
        $this->startDate = now()->subDays(7)->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
        $this->hasSavingsPreference = strtolower(auth()->user()->partner->Access_Type) === 'savings';
    }

    public function render()
    {
        return view('livewire.reports.transactions-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new TransactionsExport($this->getFilters()), $this->getExcelFilename());
    }

    public function printReport()
    {
        $filters = $this->getFilters();
        $records = app(GetTransactionsReportDetailsAction::class)
            ->filters($filters)
            ->execute();

        $filters['startDate'] = Carbon::parse($this->startDate)->format('d-m-Y');
        $filters['endDate'] = Carbon::parse($this->endDate)->format('d-m-Y');

        return app(PdfGeneratorService::class)
            ->view('pdf.transactions-report', [
                'records' => $records,
                'partnerName' => auth()->user()->partner->Institution_Name,
                'filters' => $filters,
            ])
            ->streamFromLivewire();
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetTransactionsReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }

    protected function getFilters(): array
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'transactionStatus' => $this->transactionStatus,
            'hasSavingsPreference' => $this->hasSavingsPreference
        ];
    }
}
