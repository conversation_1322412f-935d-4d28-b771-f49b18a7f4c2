<?php

namespace App\Jobs\ChatBot;

use App\Enums\DocumentType;
use App\Models\SavingsApplication;
use App\Models\WhatsApp\BotChat;
use App\Services\ChatBot\SpiroChatBotService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Twilio\Exceptions\TwilioException;
use Twilio\Rest\Client;

/**
 * Sends a WhatsApp message to the user about the status of their application
 */
class SendApplicationStatus implements ShouldQueue
{
    use Queueable;

    protected Client $twilio;
    /**
     * Create a new job instance.
     */
    public function __construct(protected array $details)
    {
        $this->twilio = new Client(config('services.twilio.account'), config('services.twilio.token'));
    }

    /**
     * Execute the job.
     */
    public function handle(SpiroChatBotService $chatbot): void
    {
        try {
            if (strtolower(Arr::get($this->details, 'status')) === 'approved') {
                $message = "*Documents Approved*\nCongratulations! Your documents have been approved.";
            } else {
                $message = "*Documents Rejected*\nSorry! Your documents have been rejected. Reason:".Arr::get($this->details, 'reason');
            }

            $chatbot->send(Arr::get($this->details, 'phone_number'), $message);
        } catch (\Exception $e) {
            logger($e->getMessage());
            logger($e->getTraceAsString());
        }
    }
}
