# Technical Architecture Document - LMS System

## 1. System Overview

### 1.1 Architecture Pattern
The LMS follows a **Modular Monolithic Architecture** built on Laravel 11, providing:
- Clear separation of concerns through modules
- Shared database with proper data isolation via partner scoping
- Centralized authentication and authorization
- Unified logging and monitoring

### 1.2 Core Principles
- **Single Responsibility**: Each class/module has one clear purpose
- **Partner Isolation**: All data is scoped by partner using global scopes
- **Double-Entry Accounting**: All financial transactions follow accounting principles
- **Event-Driven**: Use Laravel events for decoupled system notifications
- **API-First**: Core functionality exposed via REST APIs

## 2. Application Layers

### 2.1 Presentation Layer
```
Web Interface (Livewire + Blade)
├── Dashboard Components
├── Report Components  
├── Form Components
└── Data Table Components

API Layer (REST)
├── Authentication (Sanctum)
├── Partner Endpoints
├── Customer Endpoints
└── Loan Endpoints
```

### 2.2 Business Logic Layer
```
Controllers (HTTP Entry Points)
├── Web Controllers
└── API Controllers

Services (Business Logic)
├── LoanService
├── PaymentService
├── ReportService
└── NotificationService

Actions (Single Operations)
├── ApproveLoanApplicationAction
├── CreateLoanRepaymentAction
└── GenerateReportAction
```

### 2.3 Data Access Layer
```
Models (Eloquent ORM)
├── Core Models (Loan, Customer, Partner)
├── Financial Models (Transaction, JournalEntry)
├── Configuration Models (LoanProduct, Account)
└── Transactable Models (Repayment, Disbursement)

Repositories (Complex Queries)
├── LoanRepository
├── ReportRepository
└── AccountingRepository
```

## 3. Database Architecture

### 3.1 Core Entity Relationships
```
Partners (1:N) → Customers
Partners (1:N) → LoanProducts  
Partners (1:N) → Accounts
Customers (1:N) → LoanApplications
LoanApplications (1:1) → Loans
Loans (1:N) → LoanSchedules
Loans (1:N) → LoanRepayments
All Financial Operations → JournalEntries
```

### 3.2 Accounting System Design
```sql
-- Double-entry bookkeeping structure
accounts (Chart of Accounts)
├── Assets (A)
├── Liabilities (L)  
├── Capital (C)
├── Income (I)
└── Expenses (E)

journal_entries (All Transactions)
├── Debit Entries
├── Credit Entries
├── Transaction References
└── Audit Trail
```

### 3.3 Data Isolation Strategy
- **Global Scopes**: Automatic partner filtering on all models
- **Partner Context**: All operations scoped to authenticated user's partner
- **API Security**: Partner code validation in headers
- **Data Integrity**: Foreign key constraints with partner validation

## 4. Security Architecture

### 4.1 Authentication Flow
```
User Login → Laravel Auth → Session/Token → Partner Context
API Request → Sanctum Token → Partner Code Validation → Access
2FA Flow → Google2FA → QR Code → TOTP Validation
```

### 4.2 Authorization Matrix
```
Roles & Permissions (Spatie Permission)
├── Super Admin (All Partners)
├── Partner Admin (Partner Scope)
├── Loan Officer (Limited Operations)
├── Accountant (Financial Reports)
└── Customer Service (View Only)
```

### 4.3 Data Protection
- **Encryption**: Sensitive data encrypted at rest
- **HTTPS**: All communications over TLS
- **Input Validation**: Form requests with validation rules
- **SQL Injection**: Eloquent ORM with parameter binding
- **CSRF Protection**: Laravel's built-in CSRF tokens

## 5. Integration Architecture

### 5.1 Payment Service Integration
```php
PaymentServiceManager
├── MTN Mobile Money
├── Airtel Money
├── Bank Integration
└── Generic Payment Interface

interface PaymentServiceInterface
{
    public function disburse(string $phone, float $amount, string $reference): array;
    public function collect(string $phone, float $amount, string $reference): array;
    public function checkStatus(string $reference): array;
}
```

### 5.2 SMS Service Integration
```php
NotificationChannels
├── Africa's Talking SMS
├── Twilio SMS
├── Email (SMTP)
└── Database Notifications

SMS Queue Processing
├── Immediate Notifications
├── Scheduled Campaigns
├── Bulk Processing
└── Failure Handling
```

### 5.3 External API Integration
```
Partner APIs
├── Lead Materialization
├── Asset Provider Integration
├── Credit Bureau APIs
└── KYC Verification Services
```

## 6. Performance Architecture

### 6.1 Caching Strategy
```php
Cache Layers
├── Application Cache (Redis/File)
├── Database Query Cache
├── Report Data Cache
└── Session Cache

Cache Tags
├── 'reports' → Report data
├── 'loans' → Loan information
├── 'customers' → Customer data
└── 'accounts' → Account balances
```

### 6.2 Queue Architecture
```
Queue Jobs
├── Payment Processing
├── SMS Sending
├── Report Generation
├── Interest Calculation
└── Penalty Application

Queue Drivers
├── Database (Development)
├── Redis (Production)
├── SQS (Cloud)
└── Sync (Testing)
```

### 6.3 Database Optimization
```sql
-- Key Indexes
CREATE INDEX idx_loans_partner_customer ON loans(Partner_ID, Customer_ID);
CREATE INDEX idx_journal_entries_date ON journal_entries(created_at, partner_id);
CREATE INDEX idx_repayments_loan_date ON loan_repayments(Loan_ID, Transaction_Date);

-- Partitioning Strategy (Future)
PARTITION BY RANGE (YEAR(created_at))
├── p2024 VALUES LESS THAN (2025)
├── p2025 VALUES LESS THAN (2026)
└── pmax VALUES LESS THAN MAXVALUE
```

## 7. Monitoring & Observability

### 7.1 Logging Architecture
```
Log Channels
├── Application Logs (Laravel Log)
├── API Request Logs
├── Financial Transaction Logs
├── Security Event Logs
└── Performance Logs

Log Levels
├── DEBUG → Development information
├── INFO → General information
├── WARNING → Potential issues
├── ERROR → Error conditions
└── CRITICAL → System failures
```

### 7.2 Monitoring Points
```
Application Metrics
├── Response Times
├── Error Rates
├── Queue Processing Times
├── Database Query Performance
└── Memory Usage

Business Metrics
├── Loan Application Volume
├── Disbursement Success Rate
├── Repayment Collection Rate
├── API Usage Statistics
└── Partner Activity Metrics
```

## 8. Deployment Architecture

### 8.1 Environment Structure
```
Environments
├── Development (Local)
├── Testing (Staging)
├── UAT (User Acceptance)
└── Production (Live)

Configuration Management
├── Environment Variables (.env)
├── Config Files (config/)
├── Feature Flags
└── Partner-Specific Settings
```

### 8.2 Infrastructure Components
```
Web Tier
├── Load Balancer
├── Web Servers (Nginx/Apache)
└── PHP-FPM Workers

Application Tier  
├── Laravel Application
├── Queue Workers
└── Scheduled Tasks (Cron)

Data Tier
├── MySQL Database
├── Redis Cache
├── File Storage
└── Backup Systems
```

## 9. Scalability Considerations

### 9.1 Horizontal Scaling
- **Load Balancing**: Multiple application instances
- **Database Read Replicas**: Separate read/write operations
- **Queue Distribution**: Multiple queue workers
- **CDN Integration**: Static asset delivery

### 9.2 Vertical Scaling
- **Database Optimization**: Indexes, query optimization
- **Memory Management**: Efficient data structures
- **CPU Optimization**: Algorithm improvements
- **Storage Optimization**: Data archiving strategies

## 10. Disaster Recovery

### 10.1 Backup Strategy
```
Backup Types
├── Database Backups (Daily)
├── File System Backups (Weekly)
├── Configuration Backups (On Change)
└── Code Repository (Git)

Recovery Procedures
├── Point-in-Time Recovery
├── Full System Restore
├── Partial Data Recovery
└── Configuration Rollback
```

### 10.2 High Availability
- **Database Clustering**: Master-slave replication
- **Application Redundancy**: Multiple server instances
- **Failover Procedures**: Automated switching
- **Health Monitoring**: Continuous system checks

---

**Document Version**: 1.0  
**Last Updated**: 2025-06-20  
**Next Review**: 2025-09-20  
**Owner**: Technical Architecture Team
