<div class="card">
    <div class="card-header ">
        <div class="row">
            <div class="col-md-4">
                <h5 class="mb-0">Arrears Report</h5>
            </div>
            <div class="col-md-8 d-flex justify-content-end">
                <x-checkbox />
                <x-end-date />
                <x-export-buttons :with-excel="true" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <x-session-flash />
        <div class="table-responsive overflow-x-auto">
            <table id="report-table" class="table table-bordered">
                <thead>
                    <tr>
                        <th colspan="4"></th>
                        <th colspan="4" class="text-center">Outstanding</th>
                        <th colspan="4" class="text-center">Arrears</th>
                        <th colspan="3"></th>
                    </tr>
                    <tr>
                        <th>Loan #</th>
                        <th>Customer</th>
                        <th>Phone Number</th>
                        <th class="text-end">Amount Disbursed</th>
                        <th class="text-end">Principal</th>
                        <th class="text-end">Interest</th>
                        <th class="text-end">Penalty</th>
                        <th class="text-end">Total Balance</th>
                        <th class="text-end">Principal</th>
                        <th class="text-end">Interest</th>
                        <th class="text-end">Penalty</th>
                        <th class="text-end">Total Arrears</th>
                        <th class="text-end">Arrears Rate</th>
                        <th class="text-end">Days in Arrears</th>
                        <th class="text-end">Expiry Date</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($records as $record)
                        <tr>
                            <td>{{ $record->id }}</td>
                            <td>{{ $record->customer->name }}</td>
                            <td>{{ $record->customer->Telephone_Number }}</td>
                            <td class="text-end"><x-money :value="$record->Facility_Amount_Granted" /></td>
                            <td class="text-end"><x-money :value="$record->schedule_sum_principal_remaining" /></td>
                            <td class="text-end"><x-money :value="$record->schedule_sum_interest_remaining" /></td>
                            <td class="text-end"><x-money :value="$record->penalty_amount" /></td>
                            <td class="text-end"><x-money :value="$record->total_outstanding_amount" /></td>
                            <td class="text-end"><x-money :value="$record->total_principal_arrears" /></td>
                            <td class="text-end"><x-money :value="$record->total_interest_arrears" /></td>
                            <td class="text-end"><x-money :value="$record->penalty_arrears" /></td>
                            <td class="text-end"><x-money :value="$record->total_arrears_amount" /></td>
                            <td class="text-end">{{ $record->arrears_rate }}%</td>
                            <td class="text-end">{{ $record->arrear_days < 0 ? abs($record->arrear_days) : 0 }}</td>
                            <td class="text-end">{{ $record->Maturity_Date->format('d-m-Y') }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="15" class="text-center">No records found</td>
                        </tr>
                    @endforelse
                </tbody>
                <tfoot>
                    <tr>
                        <th>Totals</th>
                        <th class="text-end">{{ count($records) }}</th>
                        <th></th>
                        <th class="text-end"><x-money :value="$records->sum('Facility_Amount_Granted')" /></th>
                        <th class="text-end">{{ number_format($records->sum('schedule_sum_principal_remaining')) }}</th>
                        <th class="text-end">{{ number_format($records->sum('schedule_sum_interest_remaining')) }}</th>
                        <th class="text-end">{{ number_format($records->sum('penalty_amount')) }}</th>
                        <th class="text-end">{{ number_format($records->sum('total_outstanding_amount')) }}</th>
                        <th class="text-end">{{ number_format($records->sum('total_principal_arrears')) }}</th>
                        <th class="text-end">{{ number_format($records->sum('total_interest_arrears')) }}</th>
                        <th class="text-end">{{ number_format($records->sum('penalty_arrears')) }}</th>
                        <th class="text-end">{{ number_format($records->sum('total_arrears_amount')) }}</th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer pagination mt-5 d-flex justify-content-end">
        {{ $records->links() }}
    </div>
</div>
