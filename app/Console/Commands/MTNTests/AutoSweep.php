<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\Loans\AutoSweepLoan;
use App\Models\Loan;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class AutoSweep extends Command
{
    protected $signature = 'mtn:sweep {loanId}';
    protected $description = 'Test MTN auto sweep';

    public function handle(): int
    {
        try {
            $loan = Loan::query()->findOrFail($this->argument('loanId'));

            if ($loan->Auto_Sweep_Started) {
                $this->info('Auto sweep was already initiated on ' . $loan->Auto_Sweep_Started);

                return 1;
            }

            $swept = app(AutoSweepLoan::class)->execute($loan);

            if ($swept) {
                $this->info('Loan sweep successful');

                return 0;
            }

            $this->info('Loan sweep failed');

            return 1;
        } catch (\Exception $e) {
            $this->error($e->getMessage());

            return 1;
        }
    }

    /**
     * Ask for input with validation
     *
     * @param string $question
     * @param string $field
     * @param array $rules
     * @param string|null $errorMessage
     * @return string
     */
    private function askValid(string $question, string $field, array $rules, ?string $errorMessage = null): string
    {
        do {
            $value = $this->ask($question);
            $validator = validator([$field => $value], [$field => $rules]);

            if ($validator->fails()) {
                $this->error($errorMessage ?? $validator->errors()->first());
                continue;
            }

            return $value;
        } while (true);
    }
}
