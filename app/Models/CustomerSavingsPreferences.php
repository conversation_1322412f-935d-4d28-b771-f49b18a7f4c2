<?php

namespace App\Models;

use App\Enums\PreferencePaymentType;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

class CustomerSavingsPreferences extends Model
{
    protected $fillable = [
        'Partner_ID',
        'Saving_Product_ID',
        'Customer_ID',
        'Provider_Name',
        'Payment_Frequency',
        'Payment_Type',
        'Default_Installment',
        'Expected_Amount',
        'Location'
    ];

    public function customer(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Customer::class, 'Customer_ID');
    }

    public function formattedPaymentType(): Attribute
    {
        return Attribute::make(
            get: fn() => PreferencePaymentType::getName($this->Payment_Type),
        );
    }
}
