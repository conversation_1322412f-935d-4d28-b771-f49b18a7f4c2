<?php

namespace App\Actions\Reports;

use App\Models\Loan;
use App\Models\Customer;
use App\Models\LoanPenalty;
use Illuminate\Support\Arr;
use App\Models\JournalEntry;
use App\Models\LoanSchedule;
use Illuminate\Support\Carbon;
use App\Models\LoanDisbursement;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;

class GetLoanArrearsReportDetailsAction
{
    protected string $startDate = '';
    protected string $endDate = '';
    protected int $perPage = 0;
    protected bool $getCountOnly = false;
    protected bool $suspendedInterest = false;

    public function execute()
    {
        $query = Loan::query()
            ->with('customer')
            ->whereNotIn('Credit_Account_Status', [
                Loan::ACCOUNT_STATUS_WRITTEN_OFF,
                Loan::ACCOUNT_STATUS_FULLY_PAID_OFF
            ])
            ->withSum('schedule', 'principal_remaining')
            ->withSum('schedule', 'interest_remaining')
            ->withSum('schedule', 'total_outstanding')
            ->whereRelation('latestOutstandingPayment', function ($query) {
                $query->where('payment_due_date', '<', $this->endDate);
            })
            ->addSelect([
                'total_principal_arrears' => LoanSchedule::query()
                    ->selectRaw('sum(principal_remaining)')
                    ->whereColumn('loan_id', 'loans.id')
                    ->where('total_outstanding', '>', 0)
                    //                    ->whereDate('payment_due_date', '<', $this->endDate)
                    ->limit(1),
                'total_interest_arrears' => LoanSchedule::query()
                    ->selectRaw('sum(interest_remaining)')
                    ->whereColumn('loan_id', 'loans.id')
                    ->where('total_outstanding', '>', 0)
                    //                    ->whereDate('payment_due_date', '<', $this->endDate)
                    ->limit(1),
                'arrear_days' => LoanSchedule::query()
                    ->selectRaw('datediff(payment_due_date, ?)', [$this->endDate])
                    ->whereColumn('loan_id', 'loans.id')
                    ->where('total_outstanding', '>', 0)
                    //                    ->whereDate('payment_due_date', '<', $this->endDate)
                    ->orderBy('payment_due_date')
                    ->limit(1),
                'penalty_amount' => LoanPenalty::query()
                    ->selectRaw('sum(amount_to_pay) - sum(amount)')
                    ->whereColumn('loan_id', 'loans.id')
                    ->whereDate('created_at', '<', Carbon::parse($this->endDate)->endOfDay()->toDateTimeString())
                    ->limit(1),
                'penalty_arrears' => LoanPenalty::query()
                    ->selectRaw('sum(amount_to_pay)')
                    ->whereColumn('loan_id', 'loans.id')
                    ->whereDate('created_at', '<', Carbon::parse($this->endDate)->endOfDay()->toDateTimeString())
                    ->limit(1),
            ]);

        if ($this->suspendedInterest) {
            $query->whereRaw('datediff(?, Maturity_Date) > ?', [$this->endDate, 60]);
        }

        if ($this->getCountOnly) {
            return $query->count();
        }

        $query->orderBy(
            Customer::query()
                ->select('First_Name')
                ->whereColumn('customers.id', 'loans.customer_id')
                ->limit(1)
        );

        $query->afterQuery(function ($loans) {
            $loans->each(function ($loan) {
                $loan->total_outstanding_amount = $loan->schedule_sum_principal_remaining + $loan->schedule_sum_interest_remaining + $loan->penalty_amount;
                $loan->total_arrears_amount = $loan->total_principal_arrears + $loan->total_interest_arrears + $loan->penalty_arrears;
                $loan->arrears_rate = $loan->schedule_sum_principal_remaining > 0 ? round($loan->total_principal_arrears / $loan->schedule_sum_principal_remaining * 100, 2) : 0;
            });
        });

        if ($this->perPage > 0) {
            return $query->paginate($this->perPage);
        }

        return $query->get();
    }

    public function paginate($perPage = 100): self
    {
        $this->perPage = $perPage;

        return $this;
    }

    public function filters(array $details): self
    {
        $this->endDate = Arr::get($details, 'endDate', now()->toDateString());

        if (Carbon::parse($this->endDate)->isFuture() || empty($this->endDate)) {
            $this->endDate = now()->toDateString();
        }

        $this->suspendedInterest = Arr::get($details, 'suspendedInterest', false);

        return $this;
    }

    public function getCountOnly()
    {
        $this->getCountOnly = true;

        return $this->execute();
    }
}
