<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\CreateTestTransactionAction;
use App\Exceptions\MtnApiException;
use App\Models\Transaction;
use App\Services\MockMtnApiService;
use Illuminate\Console\Command;
use App\Services\PaymentServiceManager;

class DisburseLoan extends Command
{
    protected $signature = 'mtn:disburse {reference}';

    protected $description = 'Test MTN loan disbursement API';

    public function handle(): int
    {
        try {
            $transaction = Transaction::query()
                ->where('TXN_ID', $this->argument('reference'))
                ->first();

            $api = (new PaymentServiceManager($transaction))->paymentService;
            $response = $api->disburse($transaction->Telephone_Number, $transaction->Amount, $transaction->TXN_ID);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());
            $this->error('Stack trace:');
            $this->error($e->getTraceAsString());
            return 1;
        }
    }
}
