<?php

namespace App\Console\Commands;

use App\Models\Accounts\Account;
use App\Models\Audit;
use App\Models\CustomerSavingsPreferences;
use App\Models\FloatTopUp;
use App\Models\JournalEntry;
use App\Models\Partner;
use App\Models\SavingsAccount;
use App\Models\Transactables\SavingsDeposit;
use App\Models\Transactables\SavingsWithdraw;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetPartnerAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:reset-partner-account {partnerCode}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $partnerCode = $this->argument('partnerCode');
            if (!$partnerCode) {
                $this->error('Please provide a partner code');
                return;
            }

            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            if (!$partner) {
                $this->error('Partner with provided code not found!');
                return;
            }
            $partnerId = $partner->id;
            Account::where('partner_id', $partnerId)->update(['balance' => 0]);
            Audit::where('partner_id', $partnerId)->delete();
            FloatTopUp::where('Partner_ID', $partnerId)->forceDelete();
            JournalEntry::where('partner_id', $partnerId)->forceDelete();
            //dd('done');

            $this->info('Customer savings preferences reset successfully');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }
}
