<?php

namespace App\Console\Commands;

use App\Actions\Loans\ProcessLoanRestructureAction;
use App\Models\Transaction;
use Illuminate\Console\Command;

class LoanRestructure extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:loan-restructure';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $transaction = Transaction::find(386);
        $action = new ProcessLoanRestructureAction();
        $action->execute($transaction);
    }
}
