<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cash_sales', function (Blueprint $table) {
            $table->string('vin_no')->nullable();
            $table->string('reg_no')->nullable();
            $table->string('tin')->nullable();
            $table->string('usage')->nullable();
            $table->string('sales_executive')->nullable();
            $table->string('lead_source')->nullable();
            $table->string('financier')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cash_sales', function (Blueprint $table) {
            $table->dropColumn('vin_no');
            $table->dropColumn('reg_no');
            $table->dropColumn('tin');
            $table->dropColumn('usage');
            $table->dropColumn('sales_executive');
            $table->dropColumn('lead_source');
            $table->dropColumn('financier');
        });
    }
};
