<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetLoanAgeingReportDetailsAction;
use App\Actions\Reports\GetLoanArrearsReportDetailsAction;
use App\Actions\Reports\GetOutstandingLoanReportDetailsAction;
use App\Actions\Reports\GetPortfolioAtRiskReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Models\LoanLossProvision;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\PortfolioAtRiskExport;

class PortfolioAtRiskReport extends Component
{
    use WithPagination, ExportsData;

    public bool $excludeNotDue = false;

    public function mount()
    {
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.portfolio-at-risk-report', [
            'loans' => $this->getReportData(),
            'ageingDays' => $this->getAgeingDays(),
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.portfolio-at-risk', [
                'records' => app(GetPortfolioAtRiskReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'ageingDays' => $this->getAgeingDays(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedFilters()
            ])
            ->streamFromLivewire();
    }

    public function excelExport()
    {
        return Excel::download(new PortfolioAtRiskExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->endDate)) {
            return collect();
        }

        return app(GetPortfolioAtRiskReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }

    public function getFilters(): array
    {
        return [
            'endDate' => $this->endDate,
            'excludeNotDue' => $this->excludeNotDue,
        ];
    }

    /**
     * @return array
     */
    public function getFormattedFilters(): array
    {
        return [
            'endDate' => Carbon::parse($this->endDate)->format('d-m-Y'),
            'excludeNotDue' => $this->excludeNotDue,
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection|mixed
     */
    public function getAgeingDays(): mixed
    {
        $provisions = LoanLossProvision::query()
            ->select('id', 'minimum_days', 'maximum_days')
            ->orderBy('minimum_days')
            ->get();

        if ($provisions->isEmpty()) {
            return collect([
                ['days' => '1 - 30'],
                ['days' => '31 - 60'],
                ['days' => '61 - 90'],
                ['days' => '91 - 180'],
                ['days' => '181 - Above'],
            ])->map(fn ($days) => (object) $days);
        }

        return $provisions;
    }
}
