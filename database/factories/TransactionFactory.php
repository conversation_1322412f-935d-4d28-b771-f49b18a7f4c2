<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Partner;
use App\Models\Transaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Partner>
 */
class TransactionFactory extends Factory
{
    protected $model = Transaction::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'Partner_ID' => Partner::factory()->create()->id,
            'Type' => 'Test',
            'Status' => 'Pending',
            'Telephone_Number' => '256700000000',
            'Amount' => 5000,
            'TXN_ID' => mt_rand(1000000000, 9999999999),
        ];
    }
}
