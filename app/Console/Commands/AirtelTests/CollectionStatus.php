<?php

namespace App\Console\Commands\AirtelTests;

use App\Actions\CreateTestTransactionAction;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

class CollectionStatus extends Command
{
  protected $signature = 'airtel:collection-status {id}';

  protected $description = 'Get status of a collection from a customer';

  public function handle(): int
  {
    try {
      $transaction = Transaction::query()->where('txn_id', $this->argument('id'))->firstOrFail();
      $api = (new PaymentServiceManager($transaction))->paymentService;
      $response = $api->collectionStatus($transaction->TXN_ID);
      
      $this->info(json_encode($response, true));
      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
