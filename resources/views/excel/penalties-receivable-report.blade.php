@extends('excel.layouts')

@section('content')
    <table id="report-table" class="table table-bordered">
        <thead>
            <tr>
                <th colspan="6" style="font-size: 20px; text-align: center; padding: 2px;">{{ $partnerName }}</th>
            </tr>
            <tr>
                <th colspan="6" style="font-weight: bold; font-size: 16px; text-align: center; padding: 2px;">Penalties Receivable Report</th>
            </tr>
            <tr>
                <th colspan="6" style="font-size: 14px; text-align: center; padding: 2px;">From: {{ $filters['startDate'] }} to {{ $filters['endDate'] }}</th>
            </tr>
            <tr>
                <th colspan="6"></th>
            </tr>
            <tr>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Loan #</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Customer</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Phone Number</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Loan Amount</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Penalties</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Penalties Receivable</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Penalties Paid</th>
                <th
                    style="text-align: right; font-weight: bold; border: 1px solid black; width: 180px; padding: 2px; background-color: #999999">
                    Pending Penalties</th>
            </tr>
        </thead>
        <tbody class="">
            @forelse ($records as $record)
                <tr>
                    <td style="text-align: left">{{ $record->id }}</td>
                    <td>{{ $record->customer->name }}</td>
                    <td style="text-align: right;">{{ $record->customer->Telephone_Number }}</td>
                    <td>{{ $record->Credit_Amount }}</td>
                    <td style="text-align: right;">{{ $record->penalties_sum_amount__to__pay }}</td>
                    <td style="text-align: right;">{{ $record->penaltiesReceivable() }}</td>
                    <td style="text-align: right;">{{ $record->penaltiesPaid() }}</td>
                    <td style="text-align: right;">{{ $record->penaltiesReceivable() - $record->penaltiesPaid() }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6">No records found</td>
                </tr>
            @endforelse
        </tbody>
        <tfoot class="fw-bold">
            @php
                $penaltiesReceivableSum = $records->sum(fn($record) => $record->penaltiesReceivable());
                $penaltiesPaidSum = $records->sum(fn($record) => $record->penaltiesPaid());
            @endphp
            <tr>
                <th style="border: 1px solid black; padding: 2px; font-weight: bold;">Totals</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">{{ $records->count() }}</th>
                <th style="border: 1px solid black; padding: 2px; font-weight: bold;"></th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">{{ $records->sum('Credit_Amount') }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">{{ $records->sum('penalties_sum_amount__to__pay') }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">{{ $records->sum('penaltiesReceivableSum') }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">{{ $records->sum('penaltiesPaidSum') }}</th>
                <th style="text-align: right; border: 1px solid black; padding: 2px; font-weight: bold;">{{ $records->sum('penaltiesReceivableSum') - $records->sum('penaltiesPaidSum') }}</th>
            </tr>
        </tfoot>
    </table>
    <x-print-footer />
@endsection
