<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\Loans\OptOutCustomerAction;
use App\Services\MtnApiService;
use App\Exceptions\MtnApiException;
use App\Models\Customer;
use Illuminate\Console\Command;

class OptOut extends Command
{
    protected $signature = 'mtn:opt-out
                          {msisdn : Customer phone number (format: 256XXXXXXXXX)}';

    protected $description = 'Test MTN opt-out (unlink financial resource) API';

    public function handle(): int
    {
        try {
            $msisdn = $this->argument('msisdn');

            $customer = Customer::query()->firstWhere('Telephone_Number', $msisdn);

            $closed = app(OptOutCustomerAction::class)->execute($customer, 'CB009');

            if (! $closed) {
                $this->info('Opt out failed');

                return 1;
            }

            $this->info('Opt out successful');

            return 0;
        } catch (MtnApiException $e) {
            $this->error('MTN API Error:');
            $this->error($e->getMessage());
            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }
            return 1;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());
            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }
}
