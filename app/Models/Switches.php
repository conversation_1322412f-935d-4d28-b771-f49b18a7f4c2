<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Switches extends Model
{
    protected $fillable = [
        'name',
        'partner_id',
        'category',
        'environment',
        'status',
        'username',
        'password',
        'sender_id'
    ];

    public function partner(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }
}
