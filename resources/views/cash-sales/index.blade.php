@extends('layouts/contentNavbarLayout')

@section('icon', 'menu-icon tf-icons bx bx-money-withdraw')
@section('title', 'Cash Sales')
@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h3>Cash Sales</h3>
                <a href="{{ route('cash-sales.create') }}" class="btn btn-dark float-end">Record New Cash Sale</a>
                <a href="{{ route('cash-sales.bulk-upload.form') }}" class="btn btn-primary float-end mx-3">Record Bulk Cash
                    Sales</a>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Receipt #</th>
                            <th>Customer</th>
                            <th>Phone</th>
                            <th>Location</th>
                            <th>Amount</th>
                            <th>Receipt</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($sales as $sale)
                            <tr>
                                <td>{{ $sale->receipt_number }}</td>
                                <td>{{ $sale->customer_name }}</td>
                                <td>{{ $sale->customer_phone }}</td>
                                <td>{{ $sale->customer_location }}</td>
                                <td>{{ number_format($sale->amount, 2) }}</td>
                                <td>
                                    @if ($sale->receipt_path)
                                        <a href="{{ Storage::url($sale->receipt_path) }}" target="_blank"
                                            class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>

                                <td>{{ $sale->created_at->format('d/m/Y') }}</td>
                                <td>
                                    <a href="{{ route('cash-sales.edit', $sale) }}" class="btn btn-sm btn-primary"
                                        title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                {{ $sales->links() }}
            </div>
        </div>
    </div>
@endsection
