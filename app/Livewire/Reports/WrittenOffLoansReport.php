<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetPaidOffLoansReportDetailsAction;
use App\Actions\Reports\GetWrittenOffLoansReportDetailsAction;
use App\Exports\WrittenOffLoansExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class WrittenOffLoansReport extends Component
{
    use ExportsData, WithPagination;

    public bool $showRecoveries = false;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.written-off-loans-report', [
            'records' => $this->getReportData(),
        ]);
    }

    public function printReport()
    {
        $action = app(GetWrittenOffLoansReportDetailsAction::class)
            ->filters($this->getFilters());

        if ($this->showRecoveries) {
            $action->withRecovery();
        }

        return app(PdfGeneratorService::class)
            ->view('pdf.written-off-loans', [
                'records' => $action->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters(),
                'showRecoveries' => $this->showRecoveries
            ])
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filters = $this->getFilters();
        $filters['showRecoveries'] = $this->showRecoveries;

        return Excel::download(new WrittenOffLoansExport($filters), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        $action = app(GetWrittenOffLoansReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters());

        if ($this->showRecoveries) {
            $action->withRecovery();
        }

        return $action->execute();
    }
}
