<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetFullPaymentReportDetailsAction;
use Livewire\Component;

class FullPaymentReport extends Component
{
    public string $startDate = '';
    public string $endDate = '';

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.full-payment-report', [
            'deposits' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return redirect()->route('export.full-payments.pdf', ['startDate' => $this->startDate, 'endDate' => $this->endDate]);
    }

    public function excelExport(): true
    {
        // todo: generate excel
        session()->flash('error', 'Exporting to excel is not yet supported for this report.');

        return true;
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetFullPaymentReportDetailsAction::class)
            ->paginate()
            ->filters([
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
            ])
            ->execute();
    }
}
