<?php

namespace App\Livewire\OtherReports;

use App\Actions\OtherReports\GetSmsReportDetailsAction;
use App\Actions\OtherReports\GetTransactionsReportDetailsAction;
use App\Actions\Reports\GetFullPaymentReportDetailsAction;
use App\Exports\SmsExport;
use App\Exports\TransactionsExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class SmsReport extends Component
{
    use ExportsData;

    public function mount(): void
    {
        $this->startDate = now()->subDays(7)->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.sms-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function excelExport()
    {
        return Excel::download(new SmsExport($this->getFilters()), $this->getExcelFilename());
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.sms-report', [
                'records' => app(GetSmsReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters(),
            ])
            ->streamFromLivewire();
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetSmsReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }
}
