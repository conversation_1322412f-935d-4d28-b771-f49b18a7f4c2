<?php

namespace App\Actions\Loans;

use App\Models\Loan;
use App\Models\LoanSweep;
use App\Models\Transaction;

/**
 * Used on MTN Integration after a loan has been linked.
 */
class CompleteLoanApplication
{
    protected ?string $provider;

    public function execute(Transaction $transaction, int $partnerApplicationNumber): true
    {
        $loanApplication = $transaction->loanApplication;
        $loanApplication->update([
            'Partner_Application_Number' => $partnerApplicationNumber
        ]);
        $customer = $transaction->customer;
        $options = $customer->options;
        $options['loanaccounts'] = [
            'loanaccount' => [
                'accountnumber' => $partnerApplicationNumber,
            ]
        ];
        $customer->options = $options;
        $customer->save();

        return true;
    }
}
