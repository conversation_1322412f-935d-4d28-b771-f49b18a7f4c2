<?php

namespace App\Livewire\OtherReports;

use App\Actions\OtherReports\GetScoringReportDetailsAction;
use App\Exports\ScoringExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class ScoringReport extends Component
{
    use ExportsData, WithPagination;

    public function mount(): void
    {
        $this->startDate = now()->subDays(7)->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.scoring-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function excelExport()
    {
        return Excel::download(new ScoringExport($this->getFilters()), $this->getExcelFilename());
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.scoring-report', [
                'records' => app(GetScoringReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters(),
            ])
            ->streamFromLivewire();
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetScoringReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }
}
