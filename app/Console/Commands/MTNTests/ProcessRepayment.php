<?php

namespace App\Console\Commands\MTNTests;

use App\Actions\CreateTestTransactionAction;
use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Models\Transaction;
use App\Services\MockMtnApiService;
use App\Exceptions\MtnApiException;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class ProcessRepayment extends Command
{
    protected $signature = 'mtn:process-repayment {reference}';
    protected $description = 'Test MTN collect money from agent API';

    public function handle(): int
    {
        try {
            $transaction = Transaction::query()->firstWhere('txn_id', $this->argument('reference'));

            app(ProcessLoanRepaymentAction::class)->execute($transaction);

            $transaction->update([
                'Status' => 'Completed',
                'Payment_Reference' => $transaction->Provider_TXN_ID,
            ]);

            return 0;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());

            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }

            return 1;
        }
    }

    /**
     * Ask for input with validation
     *
     * @param string $question
     * @param string $field
     * @param array $rules
     * @param string|null $errorMessage
     * @return string
     */
    private function askValid(string $question, string $field, array $rules, ?string $errorMessage = null): string
    {
        do {
            $value = $this->ask($question);
            $validator = validator([$field => $value], [$field => $rules]);

            if ($validator->fails()) {
                $this->error($errorMessage ?? $validator->errors()->first());
                continue;
            }

            return $value;
        } while (true);
    }
}
